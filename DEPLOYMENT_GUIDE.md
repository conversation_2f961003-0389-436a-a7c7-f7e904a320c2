# 🚀 Safe Deployment Guide for Invoice System Updates

## Overview
This guide provides step-by-step instructions for safely deploying the enhanced invoice system to your shared hosting environment without affecting the current functionality.

## ⚠️ Pre-Deployment Checklist

### 1. **Backup Everything**
```bash
# Create full backup of your current application
cp -r /path/to/your/app /path/to/backup/app_backup_$(date +%Y%m%d_%H%M%S)

# Backup your database
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. **Verify PHP Version**
- Ensure your shared hosting supports **PHP 8.1** or higher
- Check with your hosting provider if needed
- Update PHP version in hosting control panel if necessary

### 3. **Check Disk Space**
- Ensure you have at least 500MB free space for the update
- Clean up old files if necessary

---

## 📋 Deployment Steps

### Step 1: Update Core Files (Safe Mode)

#### 1.1 Update Composer Dependencies
```bash
# Navigate to your application directory
cd /path/to/your/app

# Update composer.json (already done in our changes)
# Update Laravel to version 10 for PHP 8.1 compatibility
composer update --no-dev --optimize-autoloader
```

#### 1.2 Update Bootstrap Files
Replace the following files with the new Laravel 10 compatible versions:

**File: `bootstrap/app.php`**
- Replace with the Laravel 10 compatible version we created
- This fixes the PHP version compatibility issue

**Files: `app/Http/Kernel.php` and related middleware**
- Add the new middleware files we created
- These are required for Laravel 10 compatibility

### Step 2: Database Migrations (Zero Downtime)

#### 2.1 Run New Migrations
```bash
# Run migrations for new features
php artisan migrate --force

# The following migrations will be applied:
# - 2025_01_01_000001_add_category_to_permissions_table.php
# - 2025_01_01_000002_create_services_table.php  
# - 2025_01_01_000003_add_service_support_to_invoice_items.php
```

#### 2.2 Seed New Data
```bash
# Seed permissions and roles
php artisan db:seed --class=PermissionSeeder

# Seed logistics services (optional but recommended)
php artisan db:seed --class=LogisticsServiceSeeder
```

### Step 3: Update Application Files

#### 3.1 Enhanced Helper Functions
- The updated `app/helpers.php` includes new URL helper functions
- These fix image path issues on shared hosting
- **No breaking changes** - existing functions remain compatible

#### 3.2 New Service Management
- Add the new `app/Models/Service.php` model
- Add the new `app/Filament/Resources/ServiceResource.php` and related pages
- These are **additive changes** - no existing functionality is affected

#### 3.3 Enhanced Invoice Creation
- Updated `app/Filament/Client/Resources/InvoiceResource.php`
- Added support for both products and services
- **Backward compatible** - existing product-based invoices continue to work

### Step 4: Clear Caches
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

---

## 🔧 Shared Hosting Specific Instructions

### For cPanel Users:

#### 1. **File Manager Method**
1. Login to cPanel
2. Open File Manager
3. Navigate to your domain's public_html folder
4. Upload the updated files using the upload feature
5. Extract if you uploaded a zip file

#### 2. **Terminal Method** (if available)
1. Open Terminal in cPanel
2. Navigate to your application directory
3. Run the deployment commands listed above

#### 3. **FTP Method**
1. Use an FTP client (FileZilla, WinSCP, etc.)
2. Upload the updated files to your server
3. Use SSH (if available) to run the artisan commands

### For Other Hosting Providers:

#### 1. **Check PHP Version**
- Most shared hosts allow PHP version selection in control panel
- Set to PHP 8.1 or higher

#### 2. **Database Access**
- Use phpMyAdmin or similar tool to run migrations manually if artisan is not available
- Import the SQL migration files directly

#### 3. **File Permissions**
Ensure proper permissions after upload:
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chmod 644 .env
```

---

## 🛡️ Safety Measures

### 1. **Maintenance Mode**
```bash
# Put application in maintenance mode during update
php artisan down --message="System Update in Progress" --retry=60

# After successful deployment
php artisan up
```

### 2. **Rollback Plan**
If something goes wrong:
```bash
# Restore from backup
cp -r /path/to/backup/app_backup_YYYYMMDD_HHMMSS/* /path/to/your/app/

# Restore database
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql
```

### 3. **Testing Checklist**
After deployment, verify:
- [ ] Application loads without errors
- [ ] Login functionality works
- [ ] Existing invoices display correctly
- [ ] Images load properly
- [ ] New service management is accessible
- [ ] Invoice creation works with both products and services
- [ ] Role management functions correctly

---

## 📱 Post-Deployment Configuration

### 1. **Update Environment Variables**
Add to your `.env` file if not present:
```env
# Media disk configuration
MEDIA_DISK=public

# Ensure APP_URL is set correctly for your domain
APP_URL=https://yourdomain.com
```

### 2. **Configure New Features**

#### 2.1 **Role Management**
1. Login as admin
2. Navigate to "Roles & Permissions" in the admin panel
3. Review and adjust permissions as needed
4. Assign appropriate roles to existing users

#### 2.2 **Service Management**
1. Navigate to "Services" in the admin panel
2. Review the pre-seeded logistics services
3. Modify or add services specific to your business
4. Set appropriate pricing and categories

#### 2.3 **Invoice Creation**
1. Test creating a new invoice
2. Verify both product and service selection works
3. Ensure pricing calculations are correct
4. Test the new status update functionality

---

## 🚨 Troubleshooting

### Common Issues and Solutions:

#### 1. **"Class not found" errors**
```bash
composer dump-autoload --optimize
```

#### 2. **Permission denied errors**
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

#### 3. **Database connection errors**
- Verify database credentials in `.env`
- Check if database server is accessible

#### 4. **Image display issues**
- Verify `APP_URL` in `.env` is correct
- Check file permissions on uploads directory
- Test the new helper functions

#### 5. **Migration errors**
- Check database user has CREATE/ALTER permissions
- Run migrations one by one if batch fails
- Check for existing table conflicts

---

## 📞 Support Information

If you encounter issues during deployment:

1. **Check Error Logs**
   - Application logs: `storage/logs/laravel.log`
   - Server logs: Usually in cPanel or hosting control panel

2. **Common Commands for Debugging**
   ```bash
   # Check application status
   php artisan about
   
   # Verify database connection
   php artisan tinker
   >>> DB::connection()->getPdo();
   
   # Check migrations status
   php artisan migrate:status
   ```

3. **Rollback if Necessary**
   - Use the backup files created in pre-deployment
   - Contact hosting support if server-level issues occur

---

## ✅ Success Indicators

Your deployment is successful when:
- [ ] Application loads without errors
- [ ] All existing functionality works as before
- [ ] New service management is available
- [ ] Enhanced invoice creation works
- [ ] Role management is functional
- [ ] Images display correctly
- [ ] No error messages in logs

**Estimated Deployment Time: 30-60 minutes**

Remember: Take your time, follow each step carefully, and don't hesitate to rollback if you encounter issues. The backup is your safety net!
