<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReportsResource\Pages;
use App\Services\ReportingService;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class ReportsResource extends Resource
{
    protected static ?string $model = null; // No specific model as this is a reporting resource
    
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    
    protected static ?string $navigationLabel = 'Reports & Analytics';
    
    protected static ?string $slug = 'reports';
    
    protected static ?int $navigationSort = 100;

    public static function getNavigationLabel(): string
    {
        return __('Reports & Analytics');
    }

    public static function getModelLabel(): string
    {
        return __('Reports');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Reports & Analytics');
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([])->actions([])->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'revenue' => Pages\RevenueReport::route('/revenue'),
            // 'outstanding' => Pages\OutstandingReport::route('/outstanding'),
            // 'collection' => Pages\CollectionReport::route('/collection'),
            // 'client-analysis' => Pages\ClientAnalysisReport::route('/client-analysis'),
            // 'cash-flow' => Pages\CashFlowReport::route('/cash-flow'),
        ];
    }

    public static function getNavigationItems(): array
    {
        return [
            \Filament\Navigation\NavigationItem::make(static::getNavigationLabel())
                ->icon(static::getNavigationIcon())
                ->activeIcon(static::getActiveNavigationIcon())
                ->group(static::getNavigationGroup())
                ->sort(static::getNavigationSort())
                ->badge(static::getNavigationBadge(), color: static::getNavigationBadgeColor())
                ->url(static::getUrl()),
        ];
    }
}
