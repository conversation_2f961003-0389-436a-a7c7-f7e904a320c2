<?php

namespace App\Filament\Resources\ReportsResource\Pages;

use App\Filament\Resources\ReportsResource;
use Filament\Resources\Pages\Page;
use Filament\Actions\Action;

class ListReports extends Page
{
    protected static string $resource = ReportsResource::class;
    
    protected static string $view = 'filament.resources.reports.pages.list-reports';
    
    public static function getNavigationLabel(): string
    {
        return 'Reports Dashboard';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('revenue_report')
                ->label('Revenue Analytics')
                ->icon('heroicon-o-currency-dollar')
                ->color('success')
                ->url(fn () => ReportsResource::getUrl('revenue')),
                
            Action::make('outstanding_report')
                ->label('Outstanding Invoices')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('warning')
                ->url(fn () => ReportsResource::getUrl('outstanding')),
                
            Action::make('collection_report')
                ->label('Collection Analysis')
                ->icon('heroicon-o-chart-pie')
                ->color('info')
                ->url(fn () => ReportsResource::getUrl('collection')),
                
            Action::make('client_analysis')
                ->label('Client Behavior')
                ->icon('heroicon-o-users')
                ->color('primary')
                ->url(fn () => ReportsResource::getUrl('client-analysis')),
                
            Action::make('cash_flow')
                ->label('Cash Flow Projections')
                ->icon('heroicon-o-arrow-trending-up')
                ->color('gray')
                ->url(fn () => ReportsResource::getUrl('cash-flow')),
        ];
    }

    protected function getViewData(): array
    {
        // Get quick stats for the dashboard
        $reportingService = app(\App\Services\ReportingService::class);
        
        // Current month analytics
        $currentMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();
        
        $revenueAnalytics = $reportingService->getRevenueAnalytics($currentMonth, $endOfMonth);
        $outstandingAnalytics = $reportingService->getOutstandingAnalytics();
        $collectionAnalytics = $reportingService->getCollectionRateAnalytics($currentMonth, $endOfMonth);
        
        return [
            'current_month_revenue' => $revenueAnalytics['total_revenue'],
            'total_outstanding' => $outstandingAnalytics['total_outstanding'],
            'collection_rate' => $collectionAnalytics['collection_rate'],
            'overdue_amount' => $outstandingAnalytics['overdue_amount'],
            'payment_count' => $revenueAnalytics['payment_count'],
            'outstanding_count' => $outstandingAnalytics['invoice_count'],
        ];
    }
}
