<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use App\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Permission;
use App\AdminDashboardSidebarSorting;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?int $navigationSort = AdminDashboardSidebarSorting::ADMINS->value + 1;

    protected static ?string $navigationGroup = 'User Management';

    public static function getNavigationLabel(): string
    {
        return __('Roles & Permissions');
    }

    public static function getModelLabel(): string
    {
        return __('Role');
    }

    public static function getPluralLabel(): string
    {
        return __('Roles & Permissions');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Role Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Role Name')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('e.g., manager, accountant, viewer')
                            ->helperText('Use lowercase letters, numbers, and underscores only'),
                        
                        Forms\Components\TextInput::make('display_name')
                            ->label('Display Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Manager, Accountant, Viewer')
                            ->helperText('Human-readable name for this role'),
                        
                        Forms\Components\Toggle::make('is_default')
                            ->label('Default Role')
                            ->helperText('Should this role be assigned to new users by default?')
                            ->default(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Permissions')
                    ->schema([
                        Forms\Components\CheckboxList::make('permissions')
                            ->label('Assign Permissions')
                            ->relationship('permissions', 'display_name')
                            ->options(function () {
                                return Permission::all()->groupBy('category')->map(function ($permissions, $category) {
                                    return $permissions->pluck('display_name', 'id');
                                })->flatten();
                            })
                            ->columns(3)
                            ->searchable()
                            ->bulkToggleable()
                            ->helperText('Select the permissions this role should have'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('display_name')
                    ->label('Role Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('System Name')
                    ->searchable()
                    ->sortable()
                    ->color('gray')
                    ->fontFamily('mono'),
                
                Tables\Columns\IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),
                
                Tables\Columns\TextColumn::make('permissions_count')
                    ->label('Permissions')
                    ->counts('permissions')
                    ->badge()
                    ->color('info'),
                
                Tables\Columns\TextColumn::make('users_count')
                    ->label('Users')
                    ->counts('users')
                    ->badge()
                    ->color('warning'),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('Default Role')
                    ->placeholder('All roles')
                    ->trueLabel('Default roles only')
                    ->falseLabel('Non-default roles only'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Role $record) {
                        // Prevent deletion of system roles
                        if (in_array($record->name, ['admin', 'client'])) {
                            $action->cancel();
                            \Filament\Notifications\Notification::make()
                                ->danger()
                                ->title('Cannot delete system role')
                                ->body('System roles (admin, client) cannot be deleted.')
                                ->send();
                        }
                        
                        // Prevent deletion if users are assigned
                        if ($record->users()->count() > 0) {
                            $action->cancel();
                            \Filament\Notifications\Notification::make()
                                ->danger()
                                ->title('Cannot delete role')
                                ->body('This role is assigned to users and cannot be deleted.')
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Tables\Actions\DeleteBulkAction $action, $records) {
                            // Check for system roles or roles with users
                            foreach ($records as $record) {
                                if (in_array($record->name, ['admin', 'client']) || $record->users()->count() > 0) {
                                    $action->cancel();
                                    \Filament\Notifications\Notification::make()
                                        ->danger()
                                        ->title('Cannot delete roles')
                                        ->body('Some selected roles cannot be deleted (system roles or roles with assigned users).')
                                        ->send();
                                    return;
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withCount(['permissions', 'users']);
    }
}
