<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use App\Models\Role;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->before(function (Actions\DeleteAction $action) {
                    // Prevent deletion of system roles
                    if (in_array($this->record->name, ['admin', 'client'])) {
                        $action->cancel();
                        \Filament\Notifications\Notification::make()
                            ->danger()
                            ->title('Cannot delete system role')
                            ->body('System roles (admin, client) cannot be deleted.')
                            ->send();
                    }
                    
                    // Prevent deletion if users are assigned
                    if ($this->record->users()->count() > 0) {
                        $action->cancel();
                        \Filament\Notifications\Notification::make()
                            ->danger()
                            ->title('Cannot delete role')
                            ->body('This role is assigned to users and cannot be deleted.')
                            ->send();
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Ensure only one default role exists
        if ($data['is_default'] ?? false) {
            Role::where('id', '!=', $record->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        return parent::handleRecordUpdate($record, $data);
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Role updated successfully';
    }
}
