<?php

namespace App\Filament\Resources\ServiceResource\Pages;

use App\Filament\Resources\ServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewService extends ViewRecord
{
    protected static string $resource = ServiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Service Information')
                    ->schema([
                        Infolists\Components\SpatieMediaLibraryImageEntry::make('service_image')
                            ->collection(\App\Models\Service::IMAGE)
                            ->label('')
                            ->size(150),
                        
                        Infolists\Components\TextEntry::make('name')
                            ->label('Service Name'),
                        
                        Infolists\Components\TextEntry::make('code')
                            ->label('Service Code')
                            ->fontFamily('mono'),
                        
                        Infolists\Components\TextEntry::make('category.name')
                            ->label('Category')
                            ->badge(),
                        
                        Infolists\Components\TextEntry::make('unit_price')
                            ->label('Base Price')
                            ->money('USD'),
                        
                        Infolists\Components\TextEntry::make('unit_type')
                            ->label('Pricing Unit')
                            ->formatStateUsing(fn (string $state): string => \App\Models\Service::UNIT_TYPES[$state] ?? $state),
                        
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Status')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),
                        
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Pricing Tiers')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('pricing_tiers')
                            ->label('')
                            ->schema([
                                Infolists\Components\TextEntry::make('min_quantity')
                                    ->label('Min Qty'),
                                Infolists\Components\TextEntry::make('max_quantity')
                                    ->label('Max Qty')
                                    ->placeholder('Unlimited'),
                                Infolists\Components\TextEntry::make('price')
                                    ->label('Price')
                                    ->money('USD'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->pricing_tiers)),

                Infolists\Components\Section::make('Metadata')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
