<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\Service
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property int $category_id
 * @property string $unit_price
 * @property string $unit_type
 * @property string|null $description
 * @property bool $is_active
 * @property array|null $pricing_tiers
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Service extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'services';

    const IMAGE = 'service';

    protected $fillable = [
        'name',
        'code',
        'category_id',
        'unit_price',
        'unit_type',
        'description',
        'is_active',
        'pricing_tiers',
    ];

    protected $casts = [
        'name' => 'string',
        'code' => 'string',
        'category_id' => 'integer',
        'unit_price' => 'decimal:2',
        'unit_type' => 'string',
        'description' => 'string',
        'is_active' => 'boolean',
        'pricing_tiers' => 'array',
    ];

    protected $appends = ['service_image'];

    // Unit types for logistics services
    const UNIT_TYPES = [
        'per_kg' => 'Per Kilogram',
        'per_package' => 'Per Package',
        'per_mile' => 'Per Mile',
        'per_km' => 'Per Kilometer',
        'per_hour' => 'Per Hour',
        'per_day' => 'Per Day',
        'flat_rate' => 'Flat Rate',
        'per_cubic_meter' => 'Per Cubic Meter',
        'per_pallet' => 'Per Pallet',
        'per_container' => 'Per Container',
    ];

    public static $rules = [
        'name' => 'required|string|max:255',
        'code' => 'required|alpha_num|min:3|max:10|unique:services,code',
        'category_id' => 'required|exists:categories,id',
        'unit_price' => 'required|numeric|min:0',
        'unit_type' => 'required|string',
        'description' => 'nullable|string',
        'is_active' => 'boolean',
    ];

    public static $messages = [
        'code.required' => 'The service code field is required.',
        'code.unique' => 'The service code has already been taken.',
        'code.alpha_num' => 'The service code may only contain letters and numbers.',
        'unit_price.required' => 'The unit price field is required.',
        'unit_price.numeric' => 'The unit price must be a number.',
        'unit_type.required' => 'The unit type field is required.',
        'category_id.required' => 'The category field is required.',
        'category_id.exists' => 'The selected category is invalid.',
    ];

    /**
     * Get the service image attribute
     */
    public function getServiceImageAttribute(): string
    {
        /** @var Media $media */
        $media = $this->getMedia(self::IMAGE)->first();
        if (!empty($media)) {
            return $media->getFullUrl();
        }

        return getAssetUrl('images/default-service.jpg');
    }

    /**
     * Get the category that owns the service
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the invoice items for the service
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, 'service_id');
    }

    /**
     * Get the quote items for the service
     */
    public function quoteItems(): HasMany
    {
        return $this->hasMany(QuoteItem::class, 'service_id');
    }

    /**
     * Calculate price based on quantity and pricing tiers
     */
    public function calculatePrice(float $quantity): float
    {
        if (empty($this->pricing_tiers)) {
            return $this->unit_price * $quantity;
        }

        // Apply tiered pricing if configured
        foreach ($this->pricing_tiers as $tier) {
            if ($quantity >= $tier['min_quantity'] && 
                (empty($tier['max_quantity']) || $quantity <= $tier['max_quantity'])) {
                return $tier['price'] * $quantity;
            }
        }

        return $this->unit_price * $quantity;
    }

    /**
     * Get formatted unit type
     */
    public function getFormattedUnitType(): string
    {
        return self::UNIT_TYPES[$this->unit_type] ?? $this->unit_type;
    }

    /**
     * Scope for active services
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for services by category
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }
}
