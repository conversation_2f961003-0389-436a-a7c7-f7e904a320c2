<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\InvoiceStatusUpdatedMail;
use Illuminate\Support\Facades\Log;

class InvoiceStatusService
{
    /**
     * Update invoice status with validation and audit trail
     */
    public function updateStatus(Invoice $invoice, int $newStatus, ?string $reason = null, ?int $userId = null): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $invoice->status;
            $userId = $userId ?? auth()->id();

            // Validate status transition
            if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
                throw new \InvalidArgumentException("Invalid status transition from {$oldStatus} to {$newStatus}");
            }

            // Update invoice status
            $invoice->update(['status' => $newStatus]);

            // Log the status change
            $this->logStatusChange($invoice, $oldStatus, $newStatus, $reason, $userId);

            // Send notifications
            $this->sendStatusChangeNotifications($invoice, $oldStatus, $newStatus);

            // Handle payment-related status changes
            $this->handlePaymentStatusChanges($invoice, $newStatus);

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Invoice status update failed', [
                'invoice_id' => $invoice->id,
                'old_status' => $oldStatus ?? null,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if status transition is valid
     */
    private function isValidStatusTransition(int $fromStatus, int $toStatus): bool
    {
        $validTransitions = [
            Invoice::DRAFT => [Invoice::UNPAID, Invoice::DRAFT],
            Invoice::UNPAID => [Invoice::PAID, Invoice::PARTIALLY, Invoice::OVERDUE, Invoice::PROCESSING, Invoice::DRAFT],
            Invoice::PARTIALLY => [Invoice::PAID, Invoice::OVERDUE, Invoice::UNPAID],
            Invoice::PAID => [Invoice::UNPAID, Invoice::PARTIALLY], // Allow reversal for corrections
            Invoice::OVERDUE => [Invoice::PAID, Invoice::PARTIALLY, Invoice::UNPAID],
            Invoice::PROCESSING => [Invoice::PAID, Invoice::PARTIALLY, Invoice::UNPAID, Invoice::OVERDUE],
        ];

        return isset($validTransitions[$fromStatus]) && 
               in_array($toStatus, $validTransitions[$fromStatus]);
    }

    /**
     * Log status change for audit trail
     */
    private function logStatusChange(Invoice $invoice, int $oldStatus, int $newStatus, ?string $reason, ?int $userId): void
    {
        $logData = [
            'invoice_id' => $invoice->id,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'old_status_label' => Invoice::STATUS_ARR[$oldStatus] ?? 'Unknown',
            'new_status_label' => Invoice::STATUS_ARR[$newStatus] ?? 'Unknown',
            'reason' => $reason,
            'changed_by' => $userId,
            'changed_at' => now(),
        ];

        Log::info('Invoice status changed', $logData);

        // You could also store this in a dedicated audit table
        // InvoiceStatusLog::create($logData);
    }

    /**
     * Send notifications for status changes
     */
    private function sendStatusChangeNotifications(Invoice $invoice, int $oldStatus, int $newStatus): void
    {
        $invoice->load('client.user');
        
        if (!$invoice->client || !$invoice->client->user) {
            return;
        }

        $clientUserId = $invoice->client->user_id;
        $statusLabel = Invoice::STATUS_ARR[$newStatus] ?? 'Unknown';
        
        // Create notification
        $title = "Invoice #{$invoice->invoice_id} status updated to {$statusLabel}";
        
        addNotification([
            Notification::NOTIFICATION_TYPE['Invoice Updated'],
            $clientUserId,
            $title,
        ]);

        // Send email notification if enabled
        if (getSettingValue('mail_notification')) {
            try {
                $mailData = [
                    'invoice' => $invoice,
                    'oldStatus' => Invoice::STATUS_ARR[$oldStatus] ?? 'Unknown',
                    'newStatus' => $statusLabel,
                    'client' => $invoice->client->user,
                ];

                Mail::to($invoice->client->user->email)
                    ->send(new InvoiceStatusUpdatedMail($mailData));
            } catch (\Exception $e) {
                Log::error('Failed to send status change email', [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle payment-related status changes
     */
    private function handlePaymentStatusChanges(Invoice $invoice, int $newStatus): void
    {
        switch ($newStatus) {
            case Invoice::PAID:
                // When marking as paid, ensure payment records exist
                $this->ensurePaymentRecordsForPaidInvoice($invoice);
                break;
                
            case Invoice::UNPAID:
                // When reverting to unpaid, you might want to handle payment reversals
                // This depends on your business logic
                break;
        }
    }

    /**
     * Ensure payment records exist for paid invoices
     */
    private function ensurePaymentRecordsForPaidInvoice(Invoice $invoice): void
    {
        $totalPaid = $invoice->payments()
            ->where('is_approved', Payment::APPROVED)
            ->sum('amount');

        if ($totalPaid < $invoice->final_amount) {
            // Create a manual payment record for the difference
            $remainingAmount = $invoice->final_amount - $totalPaid;
            
            Payment::create([
                'invoice_id' => $invoice->id,
                'amount' => $remainingAmount,
                'payment_mode' => Payment::MANUAL,
                'payment_date' => now(),
                'notes' => 'Auto-generated payment record for status update',
                'is_approved' => Payment::APPROVED,
            ]);
        }
    }

    /**
     * Get available status transitions for an invoice
     */
    public function getAvailableStatusTransitions(Invoice $invoice): array
    {
        $currentStatus = $invoice->status;
        $validTransitions = [
            Invoice::DRAFT => [Invoice::UNPAID],
            Invoice::UNPAID => [Invoice::PAID, Invoice::PARTIALLY, Invoice::OVERDUE, Invoice::PROCESSING],
            Invoice::PARTIALLY => [Invoice::PAID, Invoice::OVERDUE, Invoice::UNPAID],
            Invoice::PAID => [Invoice::UNPAID, Invoice::PARTIALLY],
            Invoice::OVERDUE => [Invoice::PAID, Invoice::PARTIALLY, Invoice::UNPAID],
            Invoice::PROCESSING => [Invoice::PAID, Invoice::PARTIALLY, Invoice::UNPAID, Invoice::OVERDUE],
        ];

        $availableStatuses = $validTransitions[$currentStatus] ?? [];
        
        return collect($availableStatuses)->mapWithKeys(function ($status) {
            return [$status => Invoice::STATUS_ARR[$status]];
        })->toArray();
    }

    /**
     * Automatically update invoice status based on payments
     */
    public function updateStatusBasedOnPayments(Invoice $invoice): void
    {
        $totalPaid = $invoice->payments()
            ->where('is_approved', Payment::APPROVED)
            ->sum('amount');

        $finalAmount = $invoice->final_amount;
        
        if ($totalPaid >= $finalAmount) {
            $newStatus = Invoice::PAID;
        } elseif ($totalPaid > 0) {
            $newStatus = Invoice::PARTIALLY;
        } else {
            $newStatus = Invoice::UNPAID;
        }

        if ($invoice->status !== $newStatus) {
            $this->updateStatus($invoice, $newStatus, 'Auto-updated based on payment records');
        }
    }
}
