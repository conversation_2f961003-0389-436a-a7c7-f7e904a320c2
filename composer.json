{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "version": "8.1.1", "description": "The Laravel Framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "althinect/filament-spatie-roles-permissions": "^2.2", "barryvdh/laravel-dompdf": "^3.1", "bezhansalleh/filament-language-switch": "^3.1", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "laravel/framework": "^10.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "^3.1", "malzariey/filament-daterangepicker-filter": "^3.2", "mariuzzo/laravel-js-localization": "^1.11", "opcodesio/log-viewer": "^3.15", "razorpay/razorpay": "^2.9", "srmklive/paypal": "~3.0", "stripe/stripe-php": "^16.5", "tapp/filament-country-code-field": "^1.0", "tightenco/ziggy": "^2.5", "twilio/sdk": "^8.3", "unicodeveloper/laravel-paystack": "^1.2", "wandesnet/mercadopago-laravel": "^1.4", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}, "platform-check": false}, "minimum-stability": "stable", "prefer-stable": true}