<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== MANUAL SERVICES TABLE CREATION ===\n";

try {
    // Check if services table already exists
    if (Schema::hasTable('services')) {
        echo "✓ Services table already exists\n";
    } else {
        echo "Creating services table...\n";
        
        // Create services table
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 10)->unique();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->decimal('unit_price', 10, 2);
            $table->string('unit_type', 50);
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('pricing_tiers')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'category_id']);
            $table->index('code');
        });
        
        echo "✓ Services table created successfully\n";
    }
    
    // Check if invoice_items table needs service support
    echo "Checking invoice_items table for service support...\n";
    
    $columns = Schema::getColumnListing('invoice_items');
    
    if (!in_array('service_id', $columns)) {
        echo "Adding service support to invoice_items table...\n";
        
        Schema::table('invoice_items', function (Blueprint $table) {
            $table->foreignId('service_id')->nullable()->after('product_id')->constrained()->onDelete('cascade');
            $table->string('service_name')->nullable()->after('product_name');
            $table->enum('item_type', ['product', 'service'])->default('product')->after('service_name');
            
            $table->index(['item_type', 'service_id']);
            $table->index(['item_type', 'product_id']);
        });
        
        // Make product_id nullable
        DB::statement('ALTER TABLE invoice_items MODIFY product_id BIGINT UNSIGNED NULL');
        
        echo "✓ Service support added to invoice_items table\n";
    } else {
        echo "✓ Invoice_items table already has service support\n";
    }
    
    // Record migrations in migrations table
    echo "Recording migrations...\n";
    
    $migrations = [
        '2025_01_01_000002_create_services_table',
        '2025_01_01_000003_add_service_support_to_invoice_items'
    ];
    
    foreach ($migrations as $migration) {
        $exists = DB::table('migrations')->where('migration', $migration)->exists();
        if (!$exists) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => DB::table('migrations')->max('batch') + 1
            ]);
            echo "✓ Recorded migration: $migration\n";
        } else {
            echo "✓ Migration already recorded: $migration\n";
        }
    }
    
    // Create some sample services
    echo "Creating sample services...\n";
    
    $servicesCount = DB::table('services')->count();
    if ($servicesCount == 0) {
        // Get a category ID
        $categoryId = DB::table('categories')->first()->id ?? 1;
        
        $sampleServices = [
            [
                'name' => 'Web Development',
                'code' => 'WEB001',
                'category_id' => $categoryId,
                'unit_price' => 75.00,
                'unit_type' => 'hour',
                'description' => 'Professional web development services',
                'is_active' => true,
                'pricing_tiers' => json_encode([
                    ['min_hours' => 1, 'max_hours' => 10, 'price' => 75.00],
                    ['min_hours' => 11, 'max_hours' => 50, 'price' => 70.00],
                    ['min_hours' => 51, 'max_hours' => null, 'price' => 65.00]
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Consulting',
                'code' => 'CONS01',
                'category_id' => $categoryId,
                'unit_price' => 100.00,
                'unit_type' => 'hour',
                'description' => 'Business and technical consulting services',
                'is_active' => true,
                'pricing_tiers' => null,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Design Services',
                'code' => 'DES001',
                'category_id' => $categoryId,
                'unit_price' => 60.00,
                'unit_type' => 'hour',
                'description' => 'Graphic and web design services',
                'is_active' => true,
                'pricing_tiers' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];
        
        DB::table('services')->insert($sampleServices);
        echo "✓ Created " . count($sampleServices) . " sample services\n";
    } else {
        echo "✓ Services already exist ($servicesCount services found)\n";
    }
    
    echo "\n🎉 SUCCESS: Services table and support created successfully!\n";
    echo "Services available: " . DB::table('services')->count() . "\n";
    echo "Active services: " . DB::table('services')->where('is_active', true)->count() . "\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n=== MANUAL CREATION COMPLETE ===\n";
