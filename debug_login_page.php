<?php

// Get the login page source to analyze the form structure
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://invoices.sample.com/admin/login',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "=== LOGIN PAGE ANALYSIS ===\n";
echo "HTTP Code: $httpCode\n\n";

if ($httpCode === 200) {
    // Save the full response to a file for analysis
    file_put_contents('login_page_source.html', $response);
    echo "✓ Login page source saved to login_page_source.html\n\n";
    
    // Look for form elements
    echo "--- FORM ANALYSIS ---\n";
    
    // Check for standard form
    if (preg_match('/<form[^>]*>.*?<\/form>/s', $response, $formMatches)) {
        echo "✓ Standard form found\n";
        $form = $formMatches[0];
        
        // Check form action and method
        preg_match('/action="([^"]*)"/', $form, $actionMatches);
        preg_match('/method="([^"]*)"/', $form, $methodMatches);
        
        echo "Form action: " . ($actionMatches[1] ?? 'not specified') . "\n";
        echo "Form method: " . ($methodMatches[1] ?? 'not specified') . "\n";
        
        // Check for CSRF token field
        if (strpos($form, '_token') !== false) {
            echo "✓ _token field found in form\n";
        } else {
            echo "✗ _token field NOT found in form\n";
        }
    } else {
        echo "✗ No standard form found\n";
    }
    
    // Check for Livewire components
    echo "\n--- LIVEWIRE ANALYSIS ---\n";
    if (strpos($response, 'wire:') !== false) {
        echo "✓ Livewire directives found\n";
    } else {
        echo "✗ No Livewire directives found\n";
    }
    
    if (strpos($response, 'livewire') !== false) {
        echo "✓ Livewire references found\n";
    } else {
        echo "✗ No Livewire references found\n";
    }
    
    // Check for CSRF meta tag
    echo "\n--- CSRF TOKEN ANALYSIS ---\n";
    if (preg_match('/name="csrf-token" content="([^"]+)"/', $response, $csrfMatches)) {
        echo "✓ CSRF meta tag found: " . substr($csrfMatches[1], 0, 10) . "...\n";
    } else {
        echo "✗ CSRF meta tag NOT found\n";
    }
    
    // Check for _token input
    if (preg_match('/name="_token" value="([^"]+)"/', $response, $tokenMatches)) {
        echo "✓ _token input found: " . substr($tokenMatches[1], 0, 10) . "...\n";
    } else {
        echo "✗ _token input NOT found\n";
    }
    
    // Check for JavaScript CSRF token
    if (preg_match('/window\.Laravel\.csrfToken = "([^"]+)"/', $response, $jsTokenMatches)) {
        echo "✓ JavaScript CSRF token found: " . substr($jsTokenMatches[1], 0, 10) . "...\n";
    } else {
        echo "✗ JavaScript CSRF token NOT found\n";
    }
    
    // Check for Livewire CSRF
    if (preg_match('/window\.livewire_token = "([^"]+)"/', $response, $livewireTokenMatches)) {
        echo "✓ Livewire token found: " . substr($livewireTokenMatches[1], 0, 10) . "...\n";
    } else {
        echo "✗ Livewire token NOT found\n";
    }
    
} else {
    echo "✗ Failed to load login page\n";
}
