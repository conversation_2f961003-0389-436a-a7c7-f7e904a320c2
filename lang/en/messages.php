<?php

return [

    /*
    |--------------------------------------------------------------------------
    | All Titles and static string in blade files - English Language
    |--------------------------------------------------------------------------
    |
    */
    //menu.blade keys
    'dashboard' => 'Dashboard',
    'users' => 'Users',
    'settings' => 'Settings',
    'apps' => 'Apps',
    'countries' => 'Countries',
    'states' => 'States',
    'cities' => 'Cities',
    'roles' => 'Roles',
    'sign_out' => 'Sign Out',
    'language' => 'Language',
    'clients' => 'Clients',
    'products' => 'Products',
    'invoices' => 'Invoices',
    'quotes' => 'Quotes',
    'general' => 'General',
    'taxes' => 'Taxes',
    'transactions' => 'Transactions',
    'categories' => 'Categories',
    'invoice_templates' => 'Invoice Templates',
    'payments' => 'Payments',
    'payment-gateway' => 'Payment Gateway',
    'change_language' => 'Change Language',
    'clear_cache' => 'Clear Cache',
    'admin' => 'Admin',
    'admins' => 'Admins',
    'add_admin' => 'Add Admin',
    'edit_admin' => 'Edit Admin',
    'admin_details' => 'Admin Details',
    'pdf_export' => 'PDF Export',
    'tax_information' => 'Tax Information',
    'currency_reports' => 'Currency Reports',
    'client_address' => 'Client Address',
    'item' => 'Item',
    'Description' => 'Description',
    'received_amount' => 'Received Amount',
    'paid_invoices' => 'Paid Invoices',
    'unpaid_invoices' => 'Unpaid Invoices',
    'change_avatar' => 'Change avatar',
    'no_records_found' => 'No records found.',
    'registered_date' => 'Registered Date',
    'fixed' => 'Fixed',
    'percentage' => 'Percentage',
    'draft' => 'Draft',
    'unpaid' => 'Unpaid',
    'paid' => 'Paid',
    'overdue' => 'Overdue',
    'processing' => 'Processing',
    'partially paid' => 'Partially Paid',
    'select discount type' => 'Select Discount Type',
    'dear' => 'Dear',
    'i_hope_you_are_well' => 'I hope you are well',
    'please_see_attached_the_invoice' => 'Please see attached the invoice',
    'the_invoice_is_due_by' => 'The invoice is due by',
    'please_do_not_hesitate_to_get_in_touch' => 'Please don\'t hesitate to get in touch if you have any questions or need clarifications',
    'also_you_can_see_the_attachment_invoice_PDF' => 'Also you can see the attachment invoice PDF',
    'view_invoice' => 'View Invoice',
    'public_view' => 'Public View',
    'created' => 'Created',
    'converted' => 'Converted',
    'pending_payment' => 'Pending Payment',
    'all' => 'All',

    'datepicker' => [
        'all' => 'All',
        'today' => 'Today',
        'this_week' => 'This Week',
        'last_week' => 'Last Week',
        'this_month' => 'This Month',
        'last_month' => 'Last Month',
        'custom' => 'Custom Range',
        'yesterday' => 'Yesterday',
        'last_7_days' => 'Last 7 Days',
        'last_30_days' => 'Last 30 Days',
    ],

    'admin_dashboard' => [
        'dashboard' => 'Dashboard',
        'name' => 'NAME',
        'registered' => 'REGISTERED',
        'month' => 'Month',
        'year' => 'Year',
        'week' => 'Week',
        'day' => 'Day',
        'total_invoices' => 'Total Invoices',
        'total_clients' => 'Total Clients',
        'total_payments' => 'Total Payments',
        'total_products' => 'Total Products',
        'total_paid_invoices' => 'Total Paid Invoices',
        'total_unpaid_invoices' => 'Total Unpaid Invoices',
        'total_partial_paid_invoices' => 'Total Partially Paid Invoices',
        'total_overdue_invoice' => 'Total Overdue Invoices',
        'payment_overview' => 'Payment Overview',
        'no_record_found' => 'No Record Found',
        'total_amount' => 'Total Amount',
        'total_paid' => 'Total Paid',
        'total_due' => 'Total Due',
        'yearly_income_overview' => 'Yearly Income Overview',
        'monthly_income_overview' => 'Monthly Income Overview',
        'invoice_overview' => 'Invoices Overview',
        'income_overview' => 'Income Overview',
    ],
    'common' => [
        'save' => 'Save',
        'submit' => 'Submit',
        'cancel' => 'Cancel',
        'discard' => 'Discard',
        'country' => 'Country',
        'state' => 'State',
        'city' => 'City',
        'please_wait' => 'Please wait...',
        'back' => 'Back',
        'action' => 'Action',
        'new' => 'New',
        'add' => 'Add',
        'edit' => 'Edit',
        'name' => 'Name',
        'details' => 'Details',
        'service' => 'Service',
        'active' => 'Active',
        'de_active' => 'Deactive',
        'created_at' => 'Created',
        'updated_at' => 'Updated',
        'status' => 'Status',
        'filter' => 'Filter',
        'actions' => 'Actions',
        'address' => 'Address',
        'n/a' => 'N/A',
        'not_available' => 'Not Available',
        'filter_options' => 'Filter Options',
        'reset' => 'Reset',
        'payment_type' => 'Payment Type',
        'pay' => 'Pay',
        'value' => 'Value',
        'default' => 'Default',
        'allow_file_type' => 'Allowed file types',
        'save_send' => 'Save & Send',
        'save_draft' => 'Save As Draft',
        'last_update' => 'Last Update',
        'delete' => 'Delete',
        'reminder' => 'Reminder',
        'custom' => 'Custom',
        'from' => 'From',
        'to' => 'To',
        'apply' => 'Apply',
        'this_week' => 'The Week',
        'are_you_sure_delete' => 'Are you sure want to delete this',
        'deleted' => 'Deleted!',
        'has_been_deleted' => 'has been deleted.',
        'no_cancel' => 'No, Cancel',
        'yes_delete' => 'Yes, Delete!',
        'ok' => 'Ok',
        'error' => 'Error',
        'attachment' => 'Attachment',
        'invoice' => 'INVOICE',
        'email' => 'Email',
        'mo' => 'Mo',
        'regards' => 'Regards',
        'search' => 'Search',
        'filter_option' => 'Filter Option',
        'zipcode' => 'Zip Code',
        'click_here' => 'Click Here',
        'export' => 'Export',
        'all_rights_reserved' => 'All rights reserved',
        'hello' => 'Hello',
        'scan_to_pay' => 'Scan To Pay',
    ],

    'user' => [
        'profile_details' => 'Profile Details',
        'avatar' => 'Avatar',
        'full_name' => 'Full Name',
        'email' => 'Email',
        'contact_number' => 'Contact Number',
        'save_changes' => 'Save Changes',
        'setting' => 'Setting',
        'account_setting' => 'Account Settings',
        'change_password' => 'Change Password',
        'current_password' => 'Current Password',
        'new_password' => 'New Password',
        'confirm_password' => 'Confirm Password',
        'account' => 'Account',
        'user_details' => 'User Details',
        'gender' => 'Gender',
        'phone' => 'Phone',
        'profile' => 'Profile',
        'phone_number' => 'Phone Number',
    ],

    'setting' => [
        'setting' => 'Setting',
        'general' => 'General',
        'regards' => 'Regards',
        'contact_information' => 'Contact Information',
        'currency_settings' => 'Currency Settings',
        'general_details' => 'General Details',
        'clinic_name' => 'Clinic Name',
        'specialities' => 'Specialities',
        'currencies' => 'Currencies',
        'prefix' => 'Prefix',
        'address' => 'Address',
        'postal_code' => 'Postal Code',
        'gstin' => 'GSTIN',
        'app_name' => 'App Name',
        'company_name' => 'Company Name',
        'app_logo' => 'App Logo',
        'image_validation' => 'The image must be of pixel 90 x 60.',
        'company_image_validation' => 'The image must be of pixel 210 x 50.',
        'company_logo' => 'Company Logo',
        'date_format' => 'Date Format',
        'time_format' => 'Time Format',
        'timezone' => 'Time Zone',
        'decimal_separator' => 'Decimal Separator',
        'thousand_separator' => 'Thousand Separator',
        'company_address' => 'Company Address',
        'company_phone' => 'Company Phone',
        'fav_icon' => 'Favicon',
        'invoice_template' => 'Invoice Template',
        'color' => 'Color',
        'mail_notifications' => 'Mail Notifications',
        'stripe_key' => 'Stripe Key',
        'stripe_secret' => 'Stripe Secret',
        'paypal_client_id' => 'Paypal Client ID',
        'paypal_secret' => 'Paypal Secret',
        'razorpay_key' => 'Razorpay Key',
        'razorpay_secret' => 'Razorpay Secret',
        'currency_position' => 'Currency Position',
        'invoice_no_prefix' => 'Invoice No Prefix',
        'invoice_no_suffix' => 'Invoice No Suffix',
        'payment_auto_approved' => 'Payment auto approval',
        'approved' => 'Approved',
        'denied' => 'Denied',
        'waiting_for_approval' => 'Waiting For Approval',
        'select_manual_payment' => 'Select Manual Payment',
        'payment_approved' => 'Payment Approved',
        'show_currency_behind' => 'Show Currency Behind',
        'invoice_settings' => 'Invoice Settings',
        'manual_payment_approval' => 'Manual Payment Approval',
        'auto_approve' => 'Auto Approve',
        'country_code' => 'Country Code',
        'show_product_description' => 'Show Product Description',
        'stripe' => 'Stripe',
        'razorpay' => 'Razorpay',
        'paypal' => 'Paypal',
        'show_additional_address' => 'Show Additional Address In Invoice',
        'send_due_invoice_email_before_x_days' => 'Send Due Invoice Email Before X Days',
        'paystack_key' => 'Paystack Key',
        'paystack_secret' => 'Paystack Secret',
        'paystack' => 'Paystack',
        'vat_no_label' => 'VAT No Label',
        'default_language' => 'Default Language',
        '12_hour' => '12 Hour',
        '24_hour' => '24 Hour',
        'mercadopago_key' => 'Mercadopago Key',
        'mercadopago_secret' => 'Mercadopago Secret',
        'mercadopago' => 'Mercadopago',
        'mercadopago_enabled' => 'Mercadopago Enabled',
    ],

    'client' => [
        'add_user' => 'Add User',
        'role' => 'Role',
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'email' => 'Email',
        'contact_no' => 'Contact No',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'confirm_your_password' => 'Confirm Your Password',
        'gender' => 'Gender',
        'male' => 'Male',
        'female' => 'Female',
        'profile' => 'Profile',
        'edit_user' => 'Edit User',
        'client' => 'Client',
        'reset_password' => 'Reset Password',
        'reset_your_password' => 'Reset Your Password',
        'add_client' => 'Add Client',
        'edit_client' => 'Edit Client',
        'website' => 'Website',
        'address' => 'Address',
        'client_details' => 'Client Details',
        'postal_code' => 'Postal Code',
        'notes' => 'Notes',
        'note' => 'Note',
        'city' => 'City',
        'role' => 'Role',
        'state' => 'State',
        'country' => 'Country',
        'created_at' => 'Date',
    ],

    'category' => [
        'add_category' => 'Add Category',
        'edit_category' => 'Edit Category',
        'category' => 'Category',
    ],

    'product' => [
        'create_product' => 'Create Product',
        'add_product' => 'Add Product',
        'edit_product' => 'Edit Product',
        'image' => 'Image',
        'name' => 'Name',
        'code' => 'Product Code',
        'category' => 'Category',
        'price' => 'Price',
        'unit_price' => 'Unit Price',
        'description' => 'Description',
        'product' => 'Product',
        'updated_at' => 'Updated At',
        'product_name' => 'Product Name',
        'product_details' => 'Product Details',
    ],

    'invoice' => [
        'new_invoice' => 'New Invoice',
        'edit_invoice' => 'Edit Invoice',
        'client' => 'Client',
        'invoice_date' => 'Invoice Date',
        'discount' => 'Discount',
        'add' => 'Add',
        'qty' => 'Qty',
        'tax' => 'Tax',
        'price' => 'Price',
        'amount' => 'Amount',
        'invoice_id' => 'Invoice ID',
        'sub_total' => 'Sub Total',
        'total' => 'Total',
        'due_date' => 'Due Date',
        'recurring' => 'Recurring',
        'this_is_recurring_invoice' => 'This is recurring invoice',
        'total_tax' => 'Tax',
        'client_name' => 'Client Name',
        'client_email' => 'Client Email',
        'invoice_details' => 'Invoice Details',
        'add_note_term' => 'Add Note & Terms',
        'remove_note_term' => 'Remove Note & Terms',
        'note' => 'Note',
        'terms' => 'Terms',
        'print_invoice' => 'Print Invoice',
        'discount_type' => 'Discount Type',
        'invoice' => 'Invoice',
        'paid' => 'Paid',
        'due_amount' => 'Due Amount',
        'payment_method' => 'Payment Method',
        'invoice_pdf' => 'Invoice',
        'transactions' => 'Transactions',
        'download' => 'Download',
        'payment' => 'Payment',
        'overview' => 'Overview',
        'note_terms' => 'Note & Terms',
        'payment_history' => 'Payment History',
        'issue_for' => 'Issue For',
        'issue_by' => 'Issued By',
        'paid_amount' => 'Paid Amount',
        'remaining_amount' => 'Remaining Amount',
        'client_overview' => 'CLIENT OVERVIEW',
        'note_not_found' => 'Note Not Found',
        'terms_not_found' => 'Terms Not Found',
        'make_payment' => 'Make Payment',
        'excel_export' => 'Excel Export',
        'invoice_url' => 'Invoice URL',
        'invoice_number' => 'Invoice Number',
        'recurring_cycle' => 'Recurring Cycle',
        'last_recurring_on' => 'Last Recurring On',
        'recurring_invoices' => 'Recurring Invoices',
        'parent_invoice' => 'Parent Invoice',
        'stop_recurring' => 'Stop Recurring',
        'start_recurring' => 'Start Recurring',
        'fax_no' => 'Fax Number',
        'nothing_amount_yet' => 'Nothing amount yet.',
        'nothing_paid_yet' => 'Nothing paid yet.',
        'nothing_due_yet' => 'Nothing due yet.',
        'yes_send' => 'Yes, Send',
        'send_invoice' => 'Send Invoice',
        'are_you_sure_send' => 'Are you sure want to send this invoice to client ?',
        'has_been_sent' => 'has been sent.',
        'send' => 'Send',
        'amount_should_be_less_than_payable_amount' => 'Amount should be less than payable amount.',
        'payment_reminder_mail' => 'Payment Reminder Mail',
        'copy_invoice_url' => 'URL copied successfully.',
        'send_whatsapp' => 'Send WhatsApp',
        'send_invoice_in_whatsapp' => 'Send Invoice In WhatsApp',
        'phone_number' => 'Phone Number',
        'amount_due' => 'Amount Due'
    ],

    'quote' => [
        'new_quote' => 'New Quote',
        'edit_quote' => 'Edit Quote',
        'quote_date' => 'Quote Date',
        'quote_details' => 'Quote Details',
        'quote' => 'Quote',
        'quote_pdf' => 'Quote PDF',
        'quote_url' => 'Quote URL',
        'convert_to_invoice' => 'Convert To Invoice',
        'quote_number' => 'Quote Number',
        'print_quote' => 'Print Quote',
        'client' => 'Client',
        'discount' => 'Discount',
        'add' => 'Add',
        'qty' => 'Qty',
        'tax' => 'Tax',
        'price' => 'Price',
        'amount' => 'Amount',
        'quote_id' => 'Quote ID',
        'sub_total' => 'Sub Total',
        'total' => 'Total',
        'due_date' => 'Due Date',
        'recurring' => 'Recurring',
        'total_tax' => 'Tax',
        'client_name' => 'Client Name',
        'client_email' => 'Client Email',
        'add_note_term' => 'Add Note & Terms',
        'remove_note_term' => 'Remove Note & Terms',
        'note' => 'Note',
        'terms' => 'Terms',
        'discount_type' => 'Discount Type',
        'paid' => 'Paid',
        'due_amount' => 'Due Amount',
        'payment_method' => 'Payment Method',
        'transactions' => 'Transactions',
        'download' => 'Download',
        'payment' => 'Payment',
        'overview' => 'Overview',
        'note_terms' => 'Note & Terms',
        'payment_history' => 'Payment History',
        'issue_for' => 'Issue For',
        'issue_by' => 'Issued By',
        'paid_amount' => 'Paid Amount',
        'remaining_amount' => 'Remaining Amount',
        'client_overview' => 'CLIENT OVERVIEW',
        'note_not_found' => 'Note Not Found',
        'terms_not_found' => 'Terms Not Found',
        'make_payment' => 'Make Payment',
        'excel_export' => 'Excel Export',
        'quote_name' => 'Quote',
        'client_id_required' => 'The Client ID is required.',
    ],

    'tax' => [
        'tax' => 'Tax',
        'add_tax' => 'Add Tax',
        'edit_tax' => 'Edit Tax',
        'is_default' => 'Is Default',
        'yes' => 'Yes',
        'no' => 'No',
    ],

    'notification' => [
        'notifications' => 'Notifications',
        'mark_all_as_read' => 'Mark All As Read',
        'you_don`t_have_any_new_notification' => 'You don\'t have any new notification',
    ],

    'payment' => [
        'payment_date' => 'Payment Date',
        'add_payment' => 'Add Payment',
        'payable_amount' => 'Payable Amount',
        'payment_type' => 'Payment Type',
        'payment_mode' => 'Payment Method',
        'transaction_id' => 'Transaction ID',
        'payment_amount' => 'Payment Amount',
        'payment_method' => 'Payment Method',
        'edit_payment' => 'Edit Payment',
        'transaction_notes' => 'Transaction Notes',
    ],

    'currency' => [
        'add_currency' => 'Add Currency',
        'edit_currency' => 'Edit Currency',
        'icon' => 'Icon',
        'currency_code' => 'Currency Code',
        'note' => 'Note',
        'add_currency_code_as_per_three_letter_iso_code' => 'Add currency code as per three-letter ISO code',
        'you_can_find_out_here' => 'you can find out here',
        'currency' => 'Currency',
    ],

    'months' => [
        'jan' => 'Jan',
        'feb' => 'Feb',
        'mar' => 'Mar',
        'apr' => 'Apr',
        'may' => 'May',
        'jun' => 'Jun',
        'jul' => 'Jul',
        'aug' => 'Aug',
        'sep' => 'Sep',
        'oct' => 'Oct',
        'nov' => 'Nov',
        'dec' => 'Dec',
    ],
    'weekdays' => [
        'sun' => 'SUN',
        'mon' => 'MON',
        'tue' => 'TUE',
        'wed' => 'WED',
        'thu' => 'THU',
        'fri' => 'FRI',
        'sat' => 'SAT',
    ],

    'login' => [
        'login' => 'Login',
        'sign_in' => 'Sign In',
        'email' => 'Email',
        'password' => 'Password',
        'remember_me' => 'Remember me',
        'forget_your_password' => 'Forgot your password?',
    ],

    'payment_qr_codes' => [
        'payment_qr_codes' => 'Payment QR Codes',
        'add_qr_code' => 'Add QR Code',
        'add_qr' => 'Add Qr',
        'title' => 'Title',
        'qr_image' => 'QR Image',
        'default' => 'Default',
        'action' => 'Action',
        'edit_qr_code' => 'Edit QR Code',
        'qr_code' => 'QR Code',
        'payment_qr_code' => 'Payment QR Code',
    ],

    'placeholder' => [
        'valid_number' => 'Valid',
        'invalid_number' => 'Invalid number',
        'invalid_country_number' => 'Invalid country number',
        'too_short' => 'Too short',
        'too_long' => 'Too long',
        'select_tax' => 'Select Tax',
        'select_payment_qr_code' => 'Select Payment QR Code',
        'short_code_only_alpha' => 'The short code must only alphabet letters.',
    ],

    'flash' => [
        'client_cant_deleted' => 'Client can\'t be deleted.',
        'category_cant_deleted' => 'Category can\'t be deleted.',
        'product_cant_deleted' => 'Product can\'t be deleted.',
        'tax_can_not_deleted' => 'Tax can\'t be deleted.',
        'currency_cant_deleted' => 'This Currency is used somewhere else.',
        'setting_updated_successfully' => 'Setting updated successfully',
        'invoice_template_updated_successfully' => 'Invoice template updated successfully',
        'currency_saved_successfully' => 'Currency saved successfully.',
        'currency_updated_successfully' => 'Currency updated successfully',
        'currency_deleted_successfully' => 'Currency deleted successfully',
        'payment_saved_successfully' => 'Payment saved successfully.',
        'payment_updated_successfully' => 'Payment updated successfully.',
        'payment_deleted_successfully' => 'Payment deleted successfully.',
        'invoice_due_amount_retrieve_successfully' => 'Invoice due amount retrieved successfully',
        'payment_qr_code_status_updated_successfully' => 'Payment QR code status updated successfully',
        'payment_qr_code_deleted_successfully' => 'Payment QR code deleted successfully',
        'payment_qr_code_updated_successfully' => 'Payment QR code updated successfully',
        'payment_qr_code_saved_successfully' => 'Payment QR code saved successfully',
        'invoice_saved_and_sent_successfully' => 'Invoice saved & sent successfully',
        'invoice_saved_successfully' => 'Invoice saved successfully',
        'invoice_updated_and_send_successfully' => 'Invoice updated & sent successfully',
        'invoice_updated_successfully' => 'Invoice updated successfully',
        'invoice_deleted_successfully' => 'Invoice deleted successfully',
        'product_price_retrieved_successfully' => 'Product price retrieved successfully',
        'invoice_currency_retrieved_successfully' => 'Invoice currency retrieved successfully',
        'Invoice_send_successfully' => 'Invoice send successfully',
        'payment_reminder_mail_send_successfully' => 'Payment reminder mail sent successfully',
        'recurring_status_updated_successfully' => 'Recurring status updated successfully',
        'quote_saved_successfully' => 'Quote saved successfully',
        'converted_quote_can_not_editable' => 'Converted quote can not be editable',
        'quote_updated_successfully' => 'Quote updated successfully',
        'quote_deleted_successfully' => 'Quote deleted successfully',
        'converted_to_invoice_successfully' => 'Converted to invoice successfully',
        'product_created_successfully' => 'Product created successfully',
        'product_updated_successfully' => 'Product updated successfully',
        'product_deleted_successfully' => 'Product deleted successfully',
        'tax_saved_successfully' => 'Tax saved successfully',
        'tax_retrieved_successfully' => 'Tax retrieved successfully',
        'tax_updated_successfully' => 'Tax updated successfully',
        'tax_deleted_successfully' => 'Tax deleted successfully',
        'status_updated_successfully' => 'Status updated successfully',
        'category_saved_successfully' => 'Category saved successfully',
        'category_retrieved_successfully' => 'Category retrieved successfully',
        'category_updated_successfully' => 'Category updated successfully',
        'category_deleted_successfully' => 'Category deleted successfully',
        'client_created_successfully' => 'Client created successfully',
        'client_updated_successfully' => 'Client updated successfully',
        'client_deleted_successfully' => 'Client deleted successfully',
        'status_retrieved_successfully' => 'Status retrieved successfully',
        'cities_retrieved_successfully' => 'Cities retrieved successfully',
        'admin_created_successfully' => 'Admin created successfully',
        'default_admin_can_not_editable' => 'Default admin can not editable',
        'admin_updated_successfully' => 'Admin updated successfully',
        'Admin_cant_be_deleted' => 'Admin can\'t be deleted',
        'Admin_deleted_successfully' => 'Admin deleted successfully',
        'admin_profile_updated_successfully' => 'Admin profile updated successfully',
        'current_password_is_invalid' => 'Current password is invalid',
        'password_updated_successfully' => 'Password updated successfully',
        'language_updated_successfully' => 'Language updated successfully',
        'payment_overview_status_retrieved_successfully' => 'PaymentOverview status retrieved successfully',
        'yearly_income_overview_chart_data_retrieved_successfully' => 'Yearly income overview chart data retrieved successfully',
        'application_cache_cleared' => 'Application cache cleared!',
        'notification_read_successfully' => 'Notification read successfully',
        'all_notification_read_successfully' => 'All notification read successfully',
        'note_retrieved_successfully' => 'Note retrieved successfully',
        'manual_payment_approved_successfully' => 'Manual payment approved successfully',
        'manual_payment_denied_successfully' => 'Manual payment denied successfully',
        'seems_you_are_not_allowed_to_access_this_record' => 'Seems, you are not allowed to access this record.',
        'select_payment_qr_code' => 'Select Payment QR Code',
        'select_currency' => 'Select Currency',
        'select_product_or_enter_free_text' => 'Select Product or Enter free text',
        'number_of_days_for_recurring_cycle' => 'Number of Days For Recurring Cycle',
        'click_refresh_icon_generate_product_code' => 'Click refresh icon to generate product code.',
        'allowed_file_types_png_jpg_jpeg' => 'Allowed file types: png, jpg, jpeg.',
        'are_sure_want_to_delete_this_client_related_all_invoices' => 'Are you sure want to delete this client related all invoices ?',
        'paystack_token_expired' => 'The paystack token has expired. Please refresh the page and try again.',
        'country_create' => 'Country saved successfully.',
        'country_update' => 'Country updated successfully.',
        'country_delete' => 'Country deleted successfully.',
        'country_used' => 'Country already in used',
        'state_create' => 'State saved successfully.',
        'state_update' => 'State updated successfully.',
        'state_delete' => 'State deleted successfully.',
        'state_used' => 'State already in used',
        'city_create' => 'City saved successfully.',
        'city_update' => 'City updated successfully.',
        'city_delete' => 'City deleted successfully.',
        'select_invoice_template' => 'Please Select Invoice Template',
        'payment_qr_code_can_not_deleted' => 'Payment QR code can\'t be deleted.',
        'contact_number_already_exists' => 'Contact number already exists for another Client.',
    ],

    'country' => [
        'short_code' => 'Short Code',
        'phone_code' => 'Phone Code',
        'countries' => 'Countries',
        'new_country' => 'New Country',
        'edit_country' => 'Edit Country',
        'country_name' => 'Country Name',
        'no_country_found' => 'No Country Found',
        'no_country_available' => 'No Country Available',
        'country' => 'Country',
    ],
    'state' => [
        'states' => 'States',
        'new_state' => 'New State',
        'edit_state' => 'Edit State',
        'state_name' => 'State Name',
        'country_name' => 'Country Name',
        'no_state_found' => 'No State Found',
        'no_state_available' => 'No State Available',
        'state' => 'State',
    ],
    'city' => [
        'cities' => 'Cities',
        'new_city' => 'New City',
        'edit_city' => 'Edit City',
        'city_name' => 'City Name',
        'state_name' => 'State Name',
        'no_city_found' => 'No City Found',
        'no_city_available' => 'No City Available',
        'city' => 'City',
    ],

    'form' => [
        'select_country' => 'Select Country',
        'select_state' => 'Select State',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'income' => 'Income',
    ],

    'mail_content' => [
        'welcome' => 'Welcome',
        'to' => 'To',
        'your_account_has_been_successfully_created_on' => 'Your account has been successfully created on',
        'your_email_address_is' => 'Your email address is',
        'in' => 'In',
        'you_can_manage_all_of_your_invoices' => 'you can manage all of your invoices',
        'thank_for_joining_and_have_a_great_day' => 'Thank for joining and have a great day',
        'join_now' => 'Join Now',
        'dear' => 'Dear',
        'payment_received_successfully_for_invoice' => 'Payment received successfully for invoice',
        'payment_date' => 'Payment Date',
        'received_payment_amount' => 'Received Payment Amount',
        'this_is_a_confirmation_that_amount_has_received' => 'This is a confirmation that amount has been successfully received',
        'view_payment_history' => 'View Payment History',
        'please_see_attached_the_quote' => 'Please see attached the quote',
        'the_quote_is_due_by' => 'The quote is due by',
        'please_dont_hesitate_to_get_in_touch' => 'Please don\'t hesitate to get in touch if you have any questions or need clarifications',
        'view_quote' => 'View Quote',
        'i_just_wanted_to_drop_you_a_quick_note_to_remind_you_that' => 'I just wanted to drop you a quick note to remind you that',
        'in_respect_of_our_invoice' => 'in respect of our invoice',
        'view_invoice' => 'View Invoice',
        'is_due_for_payment_on' => 'is due for payment on',
    ],

];
