<!DOCTYPE html>
<html
    lang="en"
    dir="ltr"
    class="fi min-h-screen"
>
    <head>
        

        <meta charset="utf-8" />
        <meta name="csrf-token" content="G7Hqi7PquRcQ3cfTyXfRsJdiwLN4Vmj0xym4A9ze" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        
        
        <title>
            Login -  Invoice Management System
        </title>

        

        <style>
            [x-cloak=''],
            [x-cloak='x-cloak'],
            [x-cloak='1'] {
                display: none !important;
            }

            @media (max-width: 1023px) {
                [x-cloak='-lg'] {
                    display: none !important;
                }
            }

            @media (min-width: 1024px) {
                [x-cloak='lg'] {
                    display: none !important;
                }
            }
        </style>

        <link
            href="http://invoices.sample.com/css/malzariey/filament-daterangepicker-filter/date-range-picker.css?v=3.4.3.0"
            rel="stylesheet"
            data-navigate-track
        />
                <link
            href="http://invoices.sample.com/css/filament/forms/forms.css?v=********"
            rel="stylesheet"
            data-navigate-track
        />
                <link
            href="http://invoices.sample.com/css/filament/support/support.css?v=********"
            rel="stylesheet"
            data-navigate-track
        />
        
<style>
    :root {
         --danger-50:254, 242, 242;  --danger-100:254, 226, 226;  --danger-200:254, 202, 202;  --danger-300:252, 165, 165;  --danger-400:248, 113, 113;  --danger-500:239, 68, 68;  --danger-600:220, 38, 38;  --danger-700:185, 28, 28;  --danger-800:153, 27, 27;  --danger-900:127, 29, 29;  --danger-950:69, 10, 10;  --gray-50:250, 250, 250;  --gray-100:244, 244, 245;  --gray-200:228, 228, 231;  --gray-300:212, 212, 216;  --gray-400:161, 161, 170;  --gray-500:113, 113, 122;  --gray-600:82, 82, 91;  --gray-700:63, 63, 70;  --gray-800:39, 39, 42;  --gray-900:24, 24, 27;  --gray-950:9, 9, 11;  --info-50:239, 246, 255;  --info-100:219, 234, 254;  --info-200:191, 219, 254;  --info-300:147, 197, 253;  --info-400:96, 165, 250;  --info-500:59, 130, 246;  --info-600:37, 99, 235;  --info-700:29, 78, 216;  --info-800:30, 64, 175;  --info-900:30, 58, 138;  --info-950:23, 37, 84;  --primary-50:247, 248, 255;  --primary-100:240, 241, 255;  --primary-200:217, 220, 255;  --primary-300:193, 198, 255;  --primary-400:147, 156, 255;  --primary-500:101, 113, 255;  --primary-600:91, 102, 230;  --primary-700:76, 85, 191;  --primary-800:61, 68, 153;  --primary-900:49, 55, 125;  --primary-950:30, 34, 77;  --success-50:240, 253, 244;  --success-100:220, 252, 231;  --success-200:187, 247, 208;  --success-300:134, 239, 172;  --success-400:74, 222, 128;  --success-500:34, 197, 94;  --success-600:22, 163, 74;  --success-700:21, 128, 61;  --success-800:22, 101, 52;  --success-900:20, 83, 45;  --success-950:5, 46, 22;  --warning-50:255, 251, 235;  --warning-100:254, 243, 199;  --warning-200:253, 230, 138;  --warning-300:252, 211, 77;  --warning-400:251, 191, 36;  --warning-500:245, 158, 11;  --warning-600:217, 119, 6;  --warning-700:180, 83, 9;  --warning-800:146, 64, 14;  --warning-900:120, 53, 15;  --warning-950:69, 26, 3;     }
</style>

        <link
            href="http://invoices.sample.com/css/filament/filament/app.css?v=********"
            rel="stylesheet"
            data-navigate-track
        />
        
            <link rel="preconnect" href="https://fonts.bunny.net">
            <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />
        

        <style>
            :root {
                --font-family: 'Inter';
                --sidebar-width: 20rem;
                --collapsed-sidebar-width: 4.5rem;
                --default-theme-mode: system;
            }
        </style>

        
        

                    <script>
                const loadDarkMode = () => {
                    window.theme = localStorage.getItem('theme') ?? 'system'
                    if (
                        window.theme === 'dark' ||
                        (window.theme === 'system' &&
                            window.matchMedia('(prefers-color-scheme: dark)')
                                .matches)
                    ) {
                        document.documentElement.classList.add('dark')
                    }
                }

                loadDarkMode()

                document.addEventListener('livewire:navigated', loadDarkMode)
            </script>
        
        <meta name="csrf-token" content="G7Hqi7PquRcQ3cfTyXfRsJdiwLN4Vmj0xym4A9ze">
<script>
    // Set CSRF token for AJAX requests
    window.Laravel = {
        csrfToken: 'G7Hqi7PquRcQ3cfTyXfRsJdiwLN4Vmj0xym4A9ze'
    };

    // Configure Livewire before it loads
    document.addEventListener('DOMContentLoaded', function() {
        if (window.Livewire) {
            // Livewire is already loaded
            console.log('Livewire loaded successfully');
        } else {
            // Wait for Livewire to load
            document.addEventListener('livewire:init', function() {
                console.log('Livewire initialized');
            });
        }
    });
</script>

    <!-- Livewire Styles --><style >[wire\:loading][wire\:loading], [wire\:loading\.delay][wire\:loading\.delay], [wire\:loading\.inline-block][wire\:loading\.inline-block], [wire\:loading\.inline][wire\:loading\.inline], [wire\:loading\.block][wire\:loading\.block], [wire\:loading\.flex][wire\:loading\.flex], [wire\:loading\.table][wire\:loading\.table], [wire\:loading\.grid][wire\:loading\.grid], [wire\:loading\.inline-flex][wire\:loading\.inline-flex] {display: none;}[wire\:loading\.delay\.none][wire\:loading\.delay\.none], [wire\:loading\.delay\.shortest][wire\:loading\.delay\.shortest], [wire\:loading\.delay\.shorter][wire\:loading\.delay\.shorter], [wire\:loading\.delay\.short][wire\:loading\.delay\.short], [wire\:loading\.delay\.default][wire\:loading\.delay\.default], [wire\:loading\.delay\.long][wire\:loading\.delay\.long], [wire\:loading\.delay\.longer][wire\:loading\.delay\.longer], [wire\:loading\.delay\.longest][wire\:loading\.delay\.longest] {display: none;}[wire\:offline][wire\:offline] {display: none;}[wire\:dirty]:not(textarea):not(input):not(select) {display: none;}:root {--livewire-progress-bar-color: #2299dd;}[x-cloak] {display: none !important;}[wire\:cloak] {display: none !important;}</style>
</head>

    <body
        class="fi-body fi-panel-admin min-h-screen bg-gray-50 font-normal text-gray-950 antialiased dark:bg-gray-950 dark:text-white"
    >
        <div wire:snapshot="{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;t6ylX26VOYq1FL0hxUNV&quot;,&quot;name&quot;:&quot;filament-language-switch&quot;,&quot;path&quot;:&quot;admin\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;9c95260ec87edce410fa6eaf506ce52cf91fba09823022453f1cb5441e05ba84&quot;}" wire:effects="{&quot;listeners&quot;:[&quot;language-switched&quot;]}" wire:id="t6ylX26VOYq1FL0hxUNV">
    <style>
        .flags-only {
            max-width: 3rem !important;
        }

        .fls-dropdown-width {
            max-width: fit-content !important;
        }
    </style>
    <!--[if BLOCK]><![endif]-->        <div class="fls-display-on fixed w-fit flex p-4 z-50 top-0 justify-end">
            <div class="rounded-lg bg-gray-50 dark:bg-gray-950">
                <div
    x-data="{
        toggle: function (event) {
            $refs.panel?.toggle(event)
        },

        open: function (event) {
            $refs.panel?.open(event)
        },

        close: function (event) {
            $refs.panel?.close(event)
        },
    }"
    class="fi-dropdown fi-dropdown fi-user-menu" data-nosnippet="true"
>
    <div
        x-on:click="toggle"
        class="fi-dropdown-trigger flex cursor-pointer"
    >
        <div
            class="flex items-center justify-center w-9 h-9 language-switch-trigger text-primary-600 bg-primary-500/10 rounded-lg p-1 ring-2 ring-inset ring-gray-200 hover:ring-gray-300 dark:ring-gray-500 hover:dark:ring-gray-400"
            x-tooltip="{
                content: 'English',
                theme: $store.theme,
                placement: 'bottom'
            }"
        >
            <!--[if BLOCK]><![endif]-->                <img
    src="http://invoices.sample.com/images/flags/united-states.png"
    alt="English" class="object-cover object-center rounded-md"
/>
            <!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <!--[if BLOCK]><![endif]-->        <div
            x-cloak
            x-float.placement.bottom-end.flip.teleport.offset="{ offset: 8,  }"
            x-ref="panel"
            x-transition:enter-start="opacity-0"
            x-transition:leave-end="opacity-0"
                        class="fi-dropdown-panel absolute z-10 w-screen divide-y divide-gray-100 rounded-lg bg-white shadow-lg ring-1 ring-gray-950/5 transition dark:divide-white/5 dark:bg-gray-900 dark:ring-white/10 fls-dropdown-width overflow-y-auto"
            style="max-height: max-content;"
        >
            <div class="fi-dropdown-list p-1 !border-t-0 space-y-1 !p-2.5">
    <!--[if BLOCK]><![endif]-->            <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('ar')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/jordan.png"
    alt="Arabic" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Arabic
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('fr')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/france.png"
    alt="French" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            French
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('de')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/germany.png"
    alt="German" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            German
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('es')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/spain.png"
    alt="Spanish" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Spanish
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('pt')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/portugal.png"
    alt="Portuguese" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Portuguese
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('ru')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/russia.png"
    alt="Russian" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Russian
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('tr')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/turkey.png"
    alt="Turkish" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Turkish
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]-->                <button
                    type="button"
                    wire:click="changeLocale('zh')"
                    
                    class="flex items-center w-full transition-colors duration-75 rounded-md outline-none fi-dropdown-list-item whitespace-nowrap disabled:pointer-events-none disabled:opacity-70 fi-dropdown-list-item-color-gray hover:bg-gray-950/5 focus:bg-gray-950/5 dark:hover:bg-white/5 dark:focus:bg-white/5 justify-start space-x-2 rtl:space-x-reverse p-1"
                >

                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->                            <img
    src="http://invoices.sample.com/images/flags/china.png"
    alt="Chinese" class="object-cover object-center rounded-lg w-7 h-7"
/>
                        <!--[if ENDBLOCK]><![endif]-->
                        <span class="text-sm font-medium text-gray-600 hover:bg-transparent dark:text-gray-200">
                            Chinese
                        </span>

                    <!--[if ENDBLOCK]><![endif]-->
                </button>
            <!--[if ENDBLOCK]><![endif]-->
        <!--[if ENDBLOCK]><![endif]-->
</div>
        </div>
    <!--[if ENDBLOCK]><![endif]-->
</div>
            </div>
        </div>
    <!--[if ENDBLOCK]><![endif]-->
</div>

        <div class="fi-simple-layout flex min-h-screen flex-col items-center">
        
        <div
            class="fi-simple-main-ctn flex w-full flex-grow items-center justify-center"
        >
            <main
                class="fi-simple-main my-16 w-full bg-white px-6 py-12 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 sm:rounded-xl sm:px-12 max-w-lg"
            >
                <div wire:snapshot="{&quot;data&quot;:{&quot;data&quot;:[{&quot;email&quot;:null,&quot;password&quot;:null,&quot;remember&quot;:false},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areFormStateUpdateHooksDisabledForTesting&quot;:false,&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;AtKcNfgSNZffuLmYr3ka&quot;,&quot;name&quot;:&quot;filament.pages.auth.login&quot;,&quot;path&quot;:&quot;admin\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;3cc04caab280c785cf7758f90c9275596f8fc965990c7d5753da7ceccbacfa4f&quot;}" wire:effects="{&quot;url&quot;:{&quot;defaultAction&quot;:{&quot;as&quot;:&quot;action&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null},&quot;defaultActionArguments&quot;:{&quot;as&quot;:&quot;actionArguments&quot;,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null}}}" wire:id="AtKcNfgSNZffuLmYr3ka" class="fi-simple-page">
    

    <section class="grid auto-cols-fr gap-y-6">
        <header class="fi-simple-header flex flex-col items-center">
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->        <div
            class="fi-logo flex text-xl font-bold leading-5 tracking-tight text-gray-950 dark:text-white mb-4"
        >
            Invoice Management System
        </div>
    <!--[if ENDBLOCK]><![endif]-->

            

<!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]-->        <h1
            class="fi-simple-header-heading text-center text-2xl font-bold tracking-tight text-gray-950 dark:text-white"
        >
            Sign in
        </h1>
    <!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]-->        <p
            class="fi-simple-header-subheading mt-2 text-center text-sm text-gray-500 dark:text-gray-400"
        >
            or

            <a
        href="http://invoices.sample.com/admin/register"
                                class="fi-link group/link relative inline-flex items-center justify-center outline-none fi-size-md fi-link-size-md gap-1.5 fi-color-custom fi-color-primary fi-ac-action fi-ac-link-action"
    >
        
        <span class="font-semibold text-sm text-custom-600 dark:text-custom-400 group-hover/link:underline group-focus-visible/link:underline" style="--c-400:var(--primary-400);--c-600:var(--primary-600);">
            sign up for an account
        </span>

        
            </a>
        </p>
    <!--[if ENDBLOCK]><![endif]-->
</header>

        <!--[if BLOCK]><![endif]-->             <!--[if ENDBLOCK]><![endif]-->

    

    <form
    method="post"
    x-data="{ isProcessing: false }"
    x-on:submit="if (isProcessing) $event.preventDefault()"
    x-on:form-processing-started="isProcessing = true"
    x-on:form-processing-finished="isProcessing = false"
    class="fi-form grid gap-y-6" id="form" wire:submit="authenticate"
>
    <div
    style="--cols-default: repeat(1, minmax(0, 1fr));" class="grid grid-cols-[--cols-default] fi-fo-component-ctn gap-6" x-data="{}" x-on:form-validation-error.window="if ($event.detail.livewireId !== &#039;AtKcNfgSNZffuLmYr3ka&#039;) {
                return
            }

            $nextTick(() =&gt; {
                let error = $el.querySelector(&#039;[data-validation-error]&#039;)

                if (! error) {
                    return
                }

                let elementToExpand = error

                while (elementToExpand) {
                    elementToExpand.dispatchEvent(new CustomEvent(&#039;expand&#039;))

                    elementToExpand = elementToExpand.parentNode
                }

                setTimeout(
                    () =&gt;
                        error.closest(&#039;[data-field-wrapper]&#039;).scrollIntoView({
                            behavior: &#039;smooth&#039;,
                            block: &#039;start&#039;,
                            inline: &#039;start&#039;,
                        }),
                    200,
                )
        })"
>
    <!--[if BLOCK]><![endif]-->        
        <div
    style="--col-span-default: span 1 / span 1;" class="col-[--col-span-default]" wire:key="AtKcNfgSNZffuLmYr3ka.data.email.Filament\Forms\Components\TextInput"
>
    <!--[if BLOCK]><![endif]-->                <div
    data-field-wrapper
    class="fi-fo-field-wrp"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div
        class="grid gap-y-2"
    >
        <!--[if BLOCK]><![endif]-->            <div
                class="flex items-center gap-x-3 justify-between "
            >
                <!--[if BLOCK]><![endif]-->                    <label
    class="fi-fo-field-wrp-label inline-flex items-center gap-x-3" for="data.email"
>
    

    <span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">
        
        Email address<!--[if BLOCK]><![endif]--><sup class="text-danger-600 dark:text-danger-400 font-medium">*</sup>
        <!--[if ENDBLOCK]><![endif]-->
    </span>

    
</label>
                <!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </div>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->            <div
                class="grid auto-cols-fr gap-y-2"
            >
                <div
        class="fi-input-wrp flex rounded-lg shadow-sm ring-1 transition duration-75 bg-white dark:bg-white/5 [&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-2 ring-gray-950/10 dark:ring-white/20 [&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-primary-600 dark:[&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-primary-500 fi-fo-text-input overflow-hidden"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div
                class="fi-input-wrp-input min-w-0 flex-1"
    >
        <input
    class="fi-input block w-full border-none py-1.5 text-base text-gray-950 transition duration-75 placeholder:text-gray-400 focus:ring-0 disabled:text-gray-500 disabled:[-webkit-text-fill-color:theme(colors.gray.500)] disabled:placeholder:[-webkit-text-fill-color:theme(colors.gray.400)] dark:text-white dark:placeholder:text-gray-500 dark:disabled:text-gray-400 dark:disabled:[-webkit-text-fill-color:theme(colors.gray.400)] dark:disabled:placeholder:[-webkit-text-fill-color:theme(colors.gray.500)] sm:text-sm sm:leading-6 bg-white/0 ps-3 pe-3" autocomplete="on" autofocus="autofocus" id="data.email" required="required" type="email" wire:model="data.email" tabindex="1"
/>
    </div>

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</div>

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    </div>
</div>

            <!--[if ENDBLOCK]><![endif]-->
</div>
            
        <div
    style="--col-span-default: span 1 / span 1;" class="col-[--col-span-default]" wire:key="AtKcNfgSNZffuLmYr3ka.data.password.Filament\Forms\Components\TextInput"
>
    <!--[if BLOCK]><![endif]-->                <div
    data-field-wrapper
    class="fi-fo-field-wrp"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div
        class="grid gap-y-2"
    >
        <!--[if BLOCK]><![endif]-->            <div
                class="flex items-center gap-x-3 justify-between "
            >
                <!--[if BLOCK]><![endif]-->                    <label
    class="fi-fo-field-wrp-label inline-flex items-center gap-x-3" for="data.password"
>
    

    <span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">
        
        Password<!--[if BLOCK]><![endif]--><sup class="text-danger-600 dark:text-danger-400 font-medium">*</sup>
        <!--[if ENDBLOCK]><![endif]-->
    </span>

    
</label>
                <!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]-->                    <div
    class="fi-fo-field-wrp-hint flex items-center gap-x-3 text-sm"
>
    <!--[if BLOCK]><![endif]-->        <span
            class="fi-fo-field-wrp-hint-label text-gray-500 fi-color-gray"
            style="--c-400:var(--gray-400);--c-600:var(--gray-600);"
        >
            <a
        href="http://invoices.sample.com/admin/password-reset/request"
                                class="fi-link group/link relative inline-flex items-center justify-center outline-none fi-size-md fi-link-size-md gap-1.5 fi-color-custom fi-color-primary" tabindex="3"
    >
        
        <span class="font-semibold text-sm text-custom-600 dark:text-custom-400 group-hover/link:underline group-focus-visible/link:underline" style="--c-400:var(--primary-400);--c-600:var(--primary-600);">
            Forgot password?
        </span>

        
            </a>
        </span>
    <!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</div>
                <!--[if ENDBLOCK]><![endif]-->
            </div>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->            <div
                class="grid auto-cols-fr gap-y-2"
            >
                <div
        class="fi-input-wrp flex rounded-lg shadow-sm ring-1 transition duration-75 bg-white dark:bg-white/5 [&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-2 ring-gray-950/10 dark:ring-white/20 [&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-primary-600 dark:[&amp;:not(:has(.fi-ac-action:focus))]:focus-within:ring-primary-500 fi-fo-text-input overflow-hidden" x-data="{ isPasswordRevealed: false }"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div
                class="fi-input-wrp-input min-w-0 flex-1"
    >
        <input
    class="fi-input block w-full border-none py-1.5 text-base text-gray-950 transition duration-75 placeholder:text-gray-400 focus:ring-0 disabled:text-gray-500 disabled:[-webkit-text-fill-color:theme(colors.gray.500)] disabled:placeholder:[-webkit-text-fill-color:theme(colors.gray.400)] dark:text-white dark:placeholder:text-gray-500 dark:disabled:text-gray-400 dark:disabled:[-webkit-text-fill-color:theme(colors.gray.400)] dark:disabled:placeholder:[-webkit-text-fill-color:theme(colors.gray.500)] sm:text-sm sm:leading-6 bg-white/0 ps-3 pe-3 [&amp;::-ms-reveal]:hidden" autocomplete="current-password" id="data.password" required="required" wire:model="data.password" x-bind:type="isPasswordRevealed ? 'text' : 'password'" tabindex="2"
/>
    </div>

    <!--[if BLOCK]><![endif]-->        <div
            class="fi-input-wrp-suffix flex items-center gap-x-3 pe-3 border-s border-gray-200 ps-3 dark:border-white/10"
        >
            <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

            <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

            <!--[if BLOCK]><![endif]-->                <div class="flex items-center gap-3">
                    <!--[if BLOCK]><![endif]-->                        <!--[if BLOCK]><![endif]-->    <button
                                style="--c-300:var(--gray-300);--c-400:var(--gray-400);--c-500:var(--gray-500);--c-600:var(--gray-600);" class="fi-icon-btn relative flex items-center justify-center rounded-lg outline-none transition duration-75 focus-visible:ring-2 -m-1.5 h-8 w-8 text-gray-400 hover:text-gray-500 focus-visible:ring-primary-600 dark:text-gray-500 dark:hover:text-gray-400 dark:focus-visible:ring-primary-500 fi-color-gray fi-ac-action fi-ac-icon-btn-action" title="Show password" type="button" wire:loading.attr="disabled" x-on:click="isPasswordRevealed = true" x-show="! isPasswordRevealed"
    >
        <!--[if BLOCK]><![endif]-->            <span class="sr-only">
                Show password
            </span>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->    <svg class="fi-icon-btn-icon h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
  <path d="M10 12.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"/>
  <path fill-rule="evenodd" d="M.664 10.59a1.651 1.651 0 0 1 0-1.186A10.004 10.004 0 0 1 10 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0 1 10 17c-4.257 0-7.893-2.66-9.336-6.41ZM14 10a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z" clip-rule="evenodd"/>
</svg><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    </button>
<!--[if ENDBLOCK]><![endif]-->

                                            <!--[if BLOCK]><![endif]-->    <button
                                style="--c-300:var(--gray-300);--c-400:var(--gray-400);--c-500:var(--gray-500);--c-600:var(--gray-600);" class="fi-icon-btn relative flex items-center justify-center rounded-lg outline-none transition duration-75 focus-visible:ring-2 -m-1.5 h-8 w-8 text-gray-400 hover:text-gray-500 focus-visible:ring-primary-600 dark:text-gray-500 dark:hover:text-gray-400 dark:focus-visible:ring-primary-500 fi-color-gray fi-ac-action fi-ac-icon-btn-action" title="Hide password" type="button" wire:loading.attr="disabled" x-on:click="isPasswordRevealed = false" x-cloak="x-cloak" x-show="isPasswordRevealed"
    >
        <!--[if BLOCK]><![endif]-->            <span class="sr-only">
                Hide password
            </span>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->    <svg class="fi-icon-btn-icon h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
  <path fill-rule="evenodd" d="M3.28 2.22a.75.75 0 0 0-1.06 1.06l14.5 14.5a.75.75 0 1 0 1.06-1.06l-1.745-1.745a10.029 10.029 0 0 0 3.3-4.38 1.651 1.651 0 0 0 0-1.185A10.004 10.004 0 0 0 9.999 3a9.956 9.956 0 0 0-4.744 1.194L3.28 2.22ZM7.752 6.69l1.092 1.092a2.5 2.5 0 0 1 3.374 3.373l1.091 1.092a4 4 0 0 0-5.557-5.557Z" clip-rule="evenodd"/>
  <path d="m10.748 13.93 2.523 2.523a9.987 9.987 0 0 1-3.27.547c-4.258 0-7.894-2.66-9.337-6.41a1.651 1.651 0 0 1 0-1.186A10.007 10.007 0 0 1 2.839 6.02L6.07 9.252a4 4 0 0 0 4.678 4.678Z"/>
</svg><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    </button>
<!--[if ENDBLOCK]><![endif]-->

                    <!--[if ENDBLOCK]><![endif]-->
                </div>
            <!--[if ENDBLOCK]><![endif]-->
        </div>
    <!--[if ENDBLOCK]><![endif]-->
</div>

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </div>
        <!--[if ENDBLOCK]><![endif]-->
    </div>
</div>

            <!--[if ENDBLOCK]><![endif]-->
</div>
            
        <div
    style="--col-span-default: span 1 / span 1;" class="col-[--col-span-default]" wire:key="AtKcNfgSNZffuLmYr3ka.data.remember.Filament\Forms\Components\Checkbox"
>
    <!--[if BLOCK]><![endif]-->                <div
    data-field-wrapper
    class="fi-fo-field-wrp"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div
        class="grid gap-y-2"
    >
        <!--[if BLOCK]><![endif]-->            <div
                class="flex items-center gap-x-3 justify-between "
            >
                <!--[if BLOCK]><![endif]-->                    <label
    class="fi-fo-field-wrp-label inline-flex items-center gap-x-3" for="data.remember"
>
    <input
    type="checkbox"
        class="fi-checkbox-input rounded border-none bg-white shadow-sm ring-1 transition duration-75 checked:ring-0 focus:ring-2 focus:ring-offset-0 disabled:pointer-events-none disabled:bg-gray-50 disabled:text-gray-50 disabled:checked:bg-gray-400 disabled:checked:text-gray-400 dark:bg-white/5 dark:disabled:bg-transparent dark:disabled:checked:bg-gray-600 text-primary-600 ring-gray-950/10 focus:ring-primary-600 checked:focus:ring-primary-500/50 dark:text-primary-500 dark:ring-white/20 dark:checked:bg-primary-500 dark:focus:ring-primary-500 dark:checked:focus:ring-primary-400/50 dark:disabled:ring-white/10" id="data.remember" wire:loading.attr="disabled" wire:model="data.remember"
/>

    <span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">
        
        Remember me<!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    </span>

    
</label>
                <!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
            </div>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>

            <!--[if ENDBLOCK]><![endif]-->
</div>
    <!--[if ENDBLOCK]><![endif]-->
</div>


        <!--[if BLOCK]><![endif]-->    <div
                class="fi-form-actions"
    >
        <!--[if BLOCK]><![endif]-->    <div
        class="fi-ac gap-3 grid grid-cols-[repeat(auto-fit,minmax(0,1fr))]"
    >
        <!--[if BLOCK]><![endif]-->            <!--[if BLOCK]><![endif]-->                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

<button
                            x-data="{
            form: null,
            isProcessing: false,
            processingMessage: null,
        }"
        x-init="
            form = $el.closest('form')

            form?.addEventListener('form-processing-started', (event) => {
                isProcessing = true
                processingMessage = event.detail.message
            })

            form?.addEventListener('form-processing-finished', () => {
                isProcessing = false
            })
        "
        x-bind:class="{ 'enabled:opacity-70 enabled:cursor-wait': isProcessing }"
        style="--c-400:var(--primary-400);--c-500:var(--primary-500);--c-600:var(--primary-600);" class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action" type="submit" wire:loading.attr="disabled" x-bind:disabled="isProcessing"
>
    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->            <svg
    fill="none"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    class="animate-spin fi-btn-icon transition duration-75 h-5 w-5 text-white" wire:loading.delay.default="" wire:target="authenticate"
>
    <path
        clip-rule="evenodd"
        d="M12 19C15.866 19 19 15.866 19 12C19 8.13401 15.866 5 12 5C8.13401 5 5 8.13401 5 12C5 15.866 8.13401 19 12 19ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill-rule="evenodd"
        fill="currentColor"
        opacity="0.2"
    ></path>
    <path
        d="M2 12C2 6.47715 6.47715 2 12 2V5C8.13401 5 5 8.13401 5 12H2Z"
        fill="currentColor"
    ></path>
</svg>
        <!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]-->            <svg
    fill="none"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    class="animate-spin fi-btn-icon transition duration-75 h-5 w-5 text-white" x-cloak="x-cloak" x-show="isProcessing"
>
    <path
        clip-rule="evenodd"
        d="M12 19C15.866 19 19 15.866 19 12C19 8.13401 15.866 5 12 5C8.13401 5 5 8.13401 5 12C5 15.866 8.13401 19 12 19ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill-rule="evenodd"
        fill="currentColor"
        opacity="0.2"
    ></path>
    <path
        d="M2 12C2 6.47715 6.47715 2 12 2V5C8.13401 5 5 8.13401 5 12H2Z"
        fill="currentColor"
    ></path>
</svg>
        <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->

    <span
                    x-show="! isProcessing"
                class="fi-btn-label"
    >
        Sign in
    </span>

    <!--[if BLOCK]><![endif]-->        <span
            x-cloak
            x-show="isProcessing"
            x-text="processingMessage"
            class="fi-btn-label"
        ></span>
    <!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</button>

            <!--[if ENDBLOCK]><![endif]-->
        <!--[if ENDBLOCK]><![endif]-->
    </div>
<!--[if ENDBLOCK]><![endif]-->
    </div>
<!--[if ENDBLOCK]><![endif]-->
</form>
    </section>

    <!--[if BLOCK]><![endif]-->        <!--[if BLOCK]><![endif]-->    <form wire:submit.prevent="callMountedAction">
        
        <div
        aria-modal="true"
    role="dialog"
    x-data="{
        isOpen: false,

        livewire: null,

        close: function () {
            this.isOpen = false

            this.$refs.modalContainer.dispatchEvent(
                new CustomEvent('modal-closed', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-action' } }),
            )
        },

        open: function () {
            this.$nextTick(() => {
                this.isOpen = true

                
                this.$refs.modalContainer.dispatchEvent(
                    new CustomEvent('modal-opened', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-action' } }),
                )
            })
        },
    }"
            x-on:close-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-action') close()"
        x-on:open-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-action') open()"
        data-fi-modal-id="AtKcNfgSNZffuLmYr3ka-action"
        x-trap.noscroll="isOpen"
    x-bind:class="{
        'fi-modal-open': isOpen,
    }"
    class="fi-modal block"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div x-cloak x-show="isOpen">
        <div
            aria-hidden="true"
            x-show="isOpen"
            x-transition.duration.300ms.opacity
            class="fi-modal-close-overlay fixed inset-0 z-40 bg-gray-950/50 dark:bg-gray-950/75"
        ></div>

        <div
            class="fixed inset-0 z-40 overflow-y-auto cursor-pointer"
        >
            <div
                x-ref="modalContainer"
                                    
                    x-on:click.self="
                        document.activeElement.selectionStart === undefined &&
                            document.activeElement.selectionEnd === undefined &&
                            $dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-action&#039; })
                    "
                                class="relative grid min-h-full grid-rows-[1fr_auto_1fr] justify-items-center sm:grid-rows-[1fr_auto_3fr] p-4" x-on:closed-form-component-action-modal.window="if (($event.detail.id === 'AtKcNfgSNZffuLmYr3ka') && $wire.mountedActions.length) open()" x-on:modal-closed.stop="if (!$event.detail?.id?.startsWith('AtKcNfgSNZffuLmYr3ka-')) {
                    return
                }

                const mountedActionShouldOpenModal = false


                if (! mountedActionShouldOpenModal) {
                    return
                }

                if ($wire.mountedFormComponentActions.length) {
                    return
                }

                $wire.unmountAction(false, false)" x-on:opened-form-component-action-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka') close()"
            >
                <div
                    x-data="{ isShown: false }"
                    x-init="
                        $nextTick(() => {
                            isShown = isOpen
                            $watch('isOpen', () => (isShown = isOpen))
                        })
                    "
                                            x-on:keydown.window.escape="$dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-action&#039; })"
                                        x-show="isShown"
                    x-transition:enter="duration-300"
                    x-transition:leave="duration-300"
                                            x-transition:enter-start="scale-95 opacity-0"
                        x-transition:enter-end="scale-100 opacity-100"
                        x-transition:leave-start="scale-100 opacity-100"
                        x-transition:leave-end="scale-95 opacity-0"
                                                                wire:key="AtKcNfgSNZffuLmYr3ka.modal.AtKcNfgSNZffuLmYr3ka-action.window"
                                        class="fi-modal-window pointer-events-auto relative row-start-2 flex w-full cursor-default flex-col bg-white shadow-xl ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 mx-auto rounded-xl hidden max-w-sm"
                >
                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
</div>
    </form>

    <!--[if ENDBLOCK]><![endif]-->

<!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

<!--[if BLOCK]><![endif]-->    <form wire:submit.prevent="callMountedInfolistAction">
        
        <div
        aria-modal="true"
    role="dialog"
    x-data="{
        isOpen: false,

        livewire: null,

        close: function () {
            this.isOpen = false

            this.$refs.modalContainer.dispatchEvent(
                new CustomEvent('modal-closed', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-infolist-action' } }),
            )
        },

        open: function () {
            this.$nextTick(() => {
                this.isOpen = true

                
                this.$refs.modalContainer.dispatchEvent(
                    new CustomEvent('modal-opened', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-infolist-action' } }),
                )
            })
        },
    }"
            x-on:close-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-infolist-action') close()"
        x-on:open-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-infolist-action') open()"
        data-fi-modal-id="AtKcNfgSNZffuLmYr3ka-infolist-action"
        x-trap.noscroll="isOpen"
    x-bind:class="{
        'fi-modal-open': isOpen,
    }"
    class="fi-modal block"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div x-cloak x-show="isOpen">
        <div
            aria-hidden="true"
            x-show="isOpen"
            x-transition.duration.300ms.opacity
            class="fi-modal-close-overlay fixed inset-0 z-40 bg-gray-950/50 dark:bg-gray-950/75"
        ></div>

        <div
            class="fixed inset-0 z-40 overflow-y-auto cursor-pointer"
        >
            <div
                x-ref="modalContainer"
                                    
                    x-on:click.self="
                        document.activeElement.selectionStart === undefined &&
                            document.activeElement.selectionEnd === undefined &&
                            $dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-infolist-action&#039; })
                    "
                                class="relative grid min-h-full grid-rows-[1fr_auto_1fr] justify-items-center sm:grid-rows-[1fr_auto_3fr] p-4" x-on:closed-form-component-action-modal.window="if (($event.detail.id === 'AtKcNfgSNZffuLmYr3ka') && $wire.mountedInfolistActions.length) open()" x-on:modal-closed.stop="if (!$event.detail?.id?.startsWith('AtKcNfgSNZffuLmYr3ka-')) {
                    return
                }

                const mountedInfolistActionShouldOpenModal = false


                if (! mountedInfolistActionShouldOpenModal) {
                    return
                }

                if ($wire.mountedFormComponentActions.length) {
                    return
                }

                $wire.unmountInfolistAction(false, false)" x-on:opened-form-component-action-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka') close()"
            >
                <div
                    x-data="{ isShown: false }"
                    x-init="
                        $nextTick(() => {
                            isShown = isOpen
                            $watch('isOpen', () => (isShown = isOpen))
                        })
                    "
                                            x-on:keydown.window.escape="$dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-infolist-action&#039; })"
                                        x-show="isShown"
                    x-transition:enter="duration-300"
                    x-transition:leave="duration-300"
                                            x-transition:enter-start="scale-95 opacity-0"
                        x-transition:enter-end="scale-100 opacity-100"
                        x-transition:leave-start="scale-100 opacity-100"
                        x-transition:leave-end="scale-95 opacity-0"
                                                                wire:key="AtKcNfgSNZffuLmYr3ka.modal.AtKcNfgSNZffuLmYr3ka-infolist-action.window"
                                        class="fi-modal-window pointer-events-auto relative row-start-2 flex w-full cursor-default flex-col bg-white shadow-xl ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 mx-auto rounded-xl hidden max-w-sm"
                >
                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
</div>
    </form>

    <!--[if ENDBLOCK]><![endif]-->

<!--[if BLOCK]><![endif]-->    
    <form wire:submit.prevent="callMountedFormComponentAction">
        <div
        aria-modal="true"
    role="dialog"
    x-data="{
        isOpen: false,

        livewire: null,

        close: function () {
            this.isOpen = false

            this.$refs.modalContainer.dispatchEvent(
                new CustomEvent('modal-closed', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-form-component-action' } }),
            )
        },

        open: function () {
            this.$nextTick(() => {
                this.isOpen = true

                
                this.$refs.modalContainer.dispatchEvent(
                    new CustomEvent('modal-opened', { detail: { id: 'AtKcNfgSNZffuLmYr3ka-form-component-action' } }),
                )
            })
        },
    }"
            x-on:close-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-form-component-action') close()"
        x-on:open-modal.window="if ($event.detail.id === 'AtKcNfgSNZffuLmYr3ka-form-component-action') open()"
        data-fi-modal-id="AtKcNfgSNZffuLmYr3ka-form-component-action"
        x-trap.noscroll="isOpen"
    x-bind:class="{
        'fi-modal-open': isOpen,
    }"
    class="fi-modal block"
>
    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

    <div x-cloak x-show="isOpen">
        <div
            aria-hidden="true"
            x-show="isOpen"
            x-transition.duration.300ms.opacity
            class="fi-modal-close-overlay fixed inset-0 z-40 bg-gray-950/50 dark:bg-gray-950/75"
        ></div>

        <div
            class="fixed inset-0 z-40 overflow-y-auto cursor-pointer"
        >
            <div
                x-ref="modalContainer"
                                    
                    x-on:click.self="
                        document.activeElement.selectionStart === undefined &&
                            document.activeElement.selectionEnd === undefined &&
                            $dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-form-component-action&#039; })
                    "
                                class="relative grid min-h-full grid-rows-[1fr_auto_1fr] justify-items-center sm:grid-rows-[1fr_auto_3fr] p-4" x-on:modal-closed.stop="if (!$event.detail?.id?.startsWith('AtKcNfgSNZffuLmYr3ka-')) {
                    return
                }

                const mountedFormComponentActionShouldOpenModal = false


                if (mountedFormComponentActionShouldOpenModal) {
                    $wire.unmountFormComponentAction(false, false)
                }"
            >
                <div
                    x-data="{ isShown: false }"
                    x-init="
                        $nextTick(() => {
                            isShown = isOpen
                            $watch('isOpen', () => (isShown = isOpen))
                        })
                    "
                                            x-on:keydown.window.escape="$dispatch(&#039;close-modal&#039;, { id: &#039;AtKcNfgSNZffuLmYr3ka-form-component-action&#039; })"
                                        x-show="isShown"
                    x-transition:enter="duration-300"
                    x-transition:leave="duration-300"
                                            x-transition:enter-start="scale-95 opacity-0"
                        x-transition:enter-end="scale-100 opacity-100"
                        x-transition:leave-start="scale-100 opacity-100"
                        x-transition:leave-end="scale-95 opacity-0"
                                                                wire:key="AtKcNfgSNZffuLmYr3ka.modal.AtKcNfgSNZffuLmYr3ka-form-component-action.window"
                                        class="fi-modal-window pointer-events-auto relative row-start-2 flex w-full cursor-default flex-col bg-white shadow-xl ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 mx-auto rounded-xl hidden max-w-sm"
                >
                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
</div>
    </form>

    <!--[if ENDBLOCK]><![endif]-->
    <!--[if ENDBLOCK]><![endif]-->

    
</div>
            </main>
        </div>

        
    </div>

        <div wire:snapshot="{&quot;data&quot;:{&quot;isFilamentNotificationsComponent&quot;:true,&quot;notifications&quot;:[[],{&quot;class&quot;:&quot;Filament\\Notifications\\Collection&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;QsbRbXENUpGl7YCQySJY&quot;,&quot;name&quot;:&quot;filament.livewire.notifications&quot;,&quot;path&quot;:&quot;admin\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a9b92c105040ce56ee1c8ea0fb902c0c203509cd7cced8e380b82e8d6324966f&quot;}" wire:effects="{&quot;listeners&quot;:[&quot;notificationsSent&quot;,&quot;notificationSent&quot;,&quot;notificationClosed&quot;]}" wire:id="QsbRbXENUpGl7YCQySJY">
    <div
        class="fi-no pointer-events-none fixed inset-4 z-50 mx-auto flex gap-3 items-end flex-col-reverse justify-end"
        role="status"
    >
        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->
</div>

        

        <script>
        window.filamentData = []    </script>

            
            <script
                src="http://invoices.sample.com/js/filament/notifications/notifications.js?v=********"
                
                
                
                
                
                
            ></script>
        
                
            <script
                src="http://invoices.sample.com/js/filament/support/support.js?v=********"
                
                
                
                
                
                
            ></script>
        
                
            <script
                src="http://invoices.sample.com/js/filament/filament/echo.js?v=********"
                
                
                
                
                
                
            ></script>
        
                
            <script
                src="http://invoices.sample.com/js/filament/filament/app.js?v=********"
                
                
                
                
                
                
            ></script>
        
    
<style>
    :root {
            }
</style>

        
                    <script>
                loadDarkMode()
            </script>
        
        
        

        
    <!-- Livewire Scripts -->
<script src="http://invoices.sample.com"   data-csrf="G7Hqi7PquRcQ3cfTyXfRsJdiwLN4Vmj0xym4A9ze" data-update-uri="/livewire/update" data-navigate-once="true"></script>
</body>
</html>
