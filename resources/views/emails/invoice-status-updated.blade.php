<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Invoice Status Updated</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        .status-paid { background-color: #d4edda; color: #155724; }
        .status-unpaid { background-color: #f8d7da; color: #721c24; }
        .status-partially { background-color: #fff3cd; color: #856404; }
        .status-overdue { background-color: #f5c6cb; color: #721c24; }
        .status-processing { background-color: #d1ecf1; color: #0c5460; }
        .status-draft { background-color: #e2e3e5; color: #383d41; }
        .invoice-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Invoice Status Updated</h1>
        <p>Hello {{ $client->first_name }} {{ $client->last_name }},</p>
        <p>The status of your invoice has been updated.</p>
    </div>

    <div class="invoice-details">
        <h3>Invoice Details</h3>
        <p><strong>Invoice Number:</strong> #{{ $invoice->invoice_id }}</p>
        <p><strong>Invoice Date:</strong> {{ $invoice->invoice_date->format('M d, Y') }}</p>
        <p><strong>Due Date:</strong> {{ $invoice->due_date->format('M d, Y') }}</p>
        <p><strong>Amount:</strong> {{ getInvoiceCurrencyAmount($invoice->final_amount, $invoice->currency_id, true) }}</p>
        
        <div style="margin: 15px 0;">
            <p><strong>Status Change:</strong></p>
            <span class="status-badge status-{{ strtolower(str_replace(' ', '-', $oldStatus)) }}">{{ $oldStatus }}</span>
            <span style="margin: 0 10px;">→</span>
            <span class="status-badge status-{{ strtolower(str_replace(' ', '-', $newStatus)) }}">{{ $newStatus }}</span>
        </div>
    </div>

    @if($newStatus === 'Paid')
        <div style="background-color: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <h4 style="color: #155724; margin-top: 0;">Payment Received</h4>
            <p style="color: #155724; margin-bottom: 0;">Thank you! Your payment has been received and processed.</p>
        </div>
    @elseif($newStatus === 'Overdue')
        <div style="background-color: #f8d7da; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <h4 style="color: #721c24; margin-top: 0;">Payment Overdue</h4>
            <p style="color: #721c24; margin-bottom: 0;">This invoice is now overdue. Please make payment as soon as possible to avoid any late fees.</p>
        </div>
    @elseif($newStatus === 'Processing')
        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <h4 style="color: #0c5460; margin-top: 0;">Payment Processing</h4>
            <p style="color: #0c5460; margin-bottom: 0;">Your payment is currently being processed. We'll notify you once it's complete.</p>
        </div>
    @endif

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ route('invoice-show-url', $invoice->id) }}" 
           style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
            View Invoice
        </a>
    </div>

    <div class="footer">
        <p>If you have any questions about this invoice, please contact us.</p>
        <p>Thank you for your business!</p>
        <hr>
        <p style="font-size: 12px;">
            This is an automated message from {{ getAppName() }}. 
            Please do not reply to this email.
        </p>
    </div>
</body>
</html>
