<meta name="csrf-token" content="{{ csrf_token() }}">
<script>
    // Set CSRF token for AJAX requests
    window.Laravel = {
        csrfToken: '{{ csrf_token() }}'
    };

    // Configure Livewire
    window.livewireScriptConfig = {
        csrf: '{{ csrf_token() }}',
        uri: '{{ config('app.url') }}',
        asset_url: '{{ config('livewire.asset_url', config('app.url')) }}'
    };

    // Set CSRF token for all AJAX requests
    if (typeof $ !== 'undefined') {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });
    }
</script>
