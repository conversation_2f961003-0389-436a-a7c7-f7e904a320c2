<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white rounded-lg shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Report Filters</h3>
                {{ $this->form }}
            </div>
        </div>

        @if(isset($reportData) && !empty($reportData))
        <!-- Summary Cards -->
        <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-4">
            <!-- Total Invoiced -->
            <div class="bg-white rounded-lg p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="flex items-center">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Invoiced</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ getCurrencyAmount($reportData['total_invoiced'] ?? 0, true) }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Total Collected -->
            <div class="bg-white rounded-lg p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="flex items-center">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                        <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Collected</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ getCurrencyAmount($reportData['total_collected'] ?? 0, true) }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Collection Rate -->
            <div class="bg-white rounded-lg p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="flex items-center">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                        <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Collection Rate</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ number_format($reportData['collection_rate'] ?? 0, 1) }}%
                        </p>
                    </div>
                </div>
            </div>

            <!-- Average Payment Time -->
            <div class="bg-white rounded-lg p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="flex items-center">
                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
                        <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Payment Time</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ number_format($reportData['average_payment_time'] ?? 0, 1) }} days
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Collection Rate Visualization -->
        <div class="bg-white rounded-lg shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Collection Performance</h3>
                <div class="h-64">
                    <canvas id="collectionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Collection Statistics -->
        <div class="grid gap-6 md:grid-cols-2">
            <!-- Invoice Statistics -->
            <div class="bg-white rounded-lg shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Invoice Statistics</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Invoices</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                {{ number_format($reportData['total_invoices_count'] ?? 0) }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Paid Invoices</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                {{ number_format($reportData['paid_invoices_count'] ?? 0) }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Payment Success Rate</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                @php
                                    $successRate = ($reportData['total_invoices_count'] ?? 0) > 0
                                        ? (($reportData['paid_invoices_count'] ?? 0) / ($reportData['total_invoices_count'] ?? 0)) * 100
                                        : 0;
                                @endphp
                                {{ number_format($successRate, 1) }}%
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Indicators -->
            <div class="bg-white rounded-lg shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Indicators</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Collection Efficiency</span>
                            <div class="flex items-center">
                                @if(($reportData['collection_rate'] ?? 0) >= 80)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                        Excellent
                                    </span>
                                @elseif(($reportData['collection_rate'] ?? 0) >= 60)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                        Good
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                        Needs Improvement
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Payment Speed</span>
                            <div class="flex items-center">
                                @if(($reportData['average_payment_time'] ?? 0) <= 30)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                        Fast
                                    </span>
                                @elseif(($reportData['average_payment_time'] ?? 0) <= 60)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                        Average
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                        Slow
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Outstanding Amount</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                {{ getCurrencyAmount(($reportData['total_invoiced'] ?? 0) - ($reportData['total_collected'] ?? 0), true) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @else
        <div class="bg-white rounded-lg p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 text-center">
            <p class="text-gray-500 dark:text-gray-400">Click "Generate Report" to view collection analytics.</p>
        </div>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if(isset($reportData) && !empty($reportData))
            const ctx = document.getElementById('collectionChart').getContext('2d');
            
            const data = {
                labels: ['Collected', 'Outstanding'],
                datasets: [{
                    data: [
                        {{ $reportData['total_collected'] ?? 0 }},
                        {{ ($reportData['total_invoiced'] ?? 0) - ($reportData['total_collected'] ?? 0) }}
                    ],
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)'
                    ],
                    borderColor: [
                        'rgb(34, 197, 94)',
                        'rgb(251, 191, 36)'
                    ],
                    borderWidth: 1
                }]
            };
            
            new Chart(ctx, {
                type: 'doughnut',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': {{ getCurrencySymbol() }}' + context.parsed.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            @endif
        });
    </script>
</x-filament-panels::page>
