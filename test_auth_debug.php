<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

try {
    echo "=== AUTHENTICATION DEBUG TEST ===\n";
    
    // Bootstrap the application
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->bootstrapWith([
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ]);
    
    echo "✓ Application bootstrapped\n";
    
    // Check if user exists
    $user = User::where('email', '<EMAIL>')->first();
    if (!$user) {
        echo "✗ User not found\n";
        exit(1);
    }
    
    echo "✓ User found: {$user->email}\n";
    echo "User ID: {$user->id}\n";
    echo "User Name: {$user->name}\n";
    echo "User Role: {$user->role}\n";

    // Check user roles relationship
    $roles = $user->roles()->get();
    echo "User roles count: " . $roles->count() . "\n";
    foreach ($roles as $role) {
        echo "Role: {$role->name} (ID: {$role->id})\n";
    }

    if ($roles->isEmpty()) {
        echo "⚠ User has no roles assigned!\n";
    }
    
    // Check password
    $password = 'password';
    $passwordCheck = Hash::check($password, $user->password);
    echo "Password check result: " . ($passwordCheck ? 'PASS' : 'FAIL') . "\n";
    
    if (!$passwordCheck) {
        echo "✗ Password verification failed\n";
        echo "Stored hash: " . substr($user->password, 0, 20) . "...\n";
        echo "Expected password: {$password}\n";
        exit(1);
    }
    
    // Check auth configuration
    echo "\n--- Authentication Configuration ---\n";
    echo "Default guard: " . config('auth.defaults.guard') . "\n";
    echo "Guard driver: " . config('auth.guards.web.driver') . "\n";
    echo "User provider: " . config('auth.guards.web.provider') . "\n";
    echo "Provider driver: " . config('auth.providers.users.driver') . "\n";
    echo "Provider model: " . config('auth.providers.users.model') . "\n";

    // Check Filament configuration
    echo "\n--- Filament Configuration ---\n";

    // Check if Filament auth is configured
    try {
        $adminPanel = \Filament\Facades\Filament::getPanel('admin');
        echo "✓ Admin panel found\n";
        echo "Auth guard: " . $adminPanel->getAuthGuard() . "\n";

        // Check auth provider
        $authProvider = $adminPanel->getAuthProvider();
        if ($authProvider) {
            echo "Auth provider: " . get_class($authProvider) . "\n";
        } else {
            echo "Auth provider: default\n";
        }

    } catch (Exception $e) {
        echo "✗ Filament panel error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
