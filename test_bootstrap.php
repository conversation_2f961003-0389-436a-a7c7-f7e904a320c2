<?php

require_once __DIR__ . '/vendor/autoload.php';

try {
    echo "=== BOOTSTRAP TEST ===\n";

    // Try to create the Laravel application
    $app = require_once __DIR__ . '/bootstrap/app.php';
    echo "✓ Application created successfully\n";

    // Try to boot the application
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✓ HTTP Kernel created successfully\n";

    // Bootstrap the application
    $app->bootstrapWith([
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ]);
    echo "✓ Application bootstrapped successfully\n";

    // Try to access configuration
    echo "APP_URL: " . config('app.url') . "\n";
    echo "LOG_CHANNEL: " . config('logging.default') . "\n";
    echo "SESSION_DRIVER: " . config('session.driver') . "\n";

    echo "✓ Bootstrap test completed successfully\n";

} catch (Exception $e) {
    echo "✗ Bootstrap failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
