<?php

// Test script to simulate browser-like login behavior
require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== BROWSER-LIKE LOGIN TEST ===\n";

// Step 1: Get login page with session
echo "Step 1: Getting login page...\n";
$getRequest = Request::create('http://invoices.sample.com/admin/login', 'GET');
$getResponse = $kernel->handle($getRequest);

// Extract CSRF token and session
$content = $getResponse->getContent();
preg_match('/name="csrf-token" content="([^"]+)"/', $content, $matches);
$csrfToken = $matches[1];

// Get session cookies
$sessionCookie = null;
$xsrfCookie = null;
foreach ($getResponse->headers->getCookies() as $cookie) {
    if ($cookie->getName() === 'invoices_session') {
        $sessionCookie = $cookie->getValue();
    }
    if ($cookie->getName() === 'XSRF-TOKEN') {
        $xsrfCookie = $cookie->getValue();
    }
}

echo "CSRF Token: " . substr($csrfToken, 0, 20) . "...\n";
echo "Session Cookie: " . ($sessionCookie ? 'SET' : 'NOT SET') . "\n";
echo "XSRF Cookie: " . ($xsrfCookie ? 'SET' : 'NOT SET') . "\n";

// Step 2: Try direct form POST (like a browser would do)
echo "\nStep 2: Testing direct form POST...\n";

$postData = [
    '_token' => $csrfToken,
    'email' => '<EMAIL>',
    'password' => 'password',
    'remember' => '0'
];

$cookies = [
    'invoices_session' => $sessionCookie,
    'XSRF-TOKEN' => $xsrfCookie
];

$postRequest = Request::create('http://invoices.sample.com/admin/login', 'POST', $postData, $cookies);
$postRequest->headers->set('Content-Type', 'application/x-www-form-urlencoded');
$postRequest->headers->set('Referer', 'http://invoices.sample.com/admin/login');

$postResponse = $kernel->handle($postRequest);

echo "POST Response Status: " . $postResponse->getStatusCode() . "\n";

if ($postResponse->getStatusCode() === 419) {
    echo "ERROR: CSRF Token Mismatch!\n";
    
    // Debug session and CSRF
    echo "\nDEBUG INFO:\n";
    echo "Request Host: " . $postRequest->getHost() . "\n";
    echo "Session Domain Config: " . config('session.domain') . "\n";
    echo "Session Cookie Name: " . config('session.cookie') . "\n";
    echo "Session Same Site: " . config('session.same_site') . "\n";
    
} elseif ($postResponse->getStatusCode() === 302) {
    echo "SUCCESS: Redirect response (likely successful login)\n";
    $location = $postResponse->headers->get('Location');
    echo "Redirect to: " . $location . "\n";
    
} elseif ($postResponse->getStatusCode() === 200) {
    echo "Form returned 200 - checking for errors...\n";
    $responseContent = $postResponse->getContent();
    if (strpos($responseContent, 'error') !== false || strpos($responseContent, 'invalid') !== false) {
        echo "Login form contains errors\n";
    } else {
        echo "Login form processed successfully\n";
    }
} else {
    echo "Unexpected status: " . $postResponse->getStatusCode() . "\n";
}

// Step 3: Check if user is authenticated after login attempt
echo "\nStep 3: Checking authentication status...\n";
try {
    // Create a new request to check auth status
    $authCheckRequest = Request::create('http://invoices.sample.com/admin', 'GET', [], $cookies);
    $authCheckResponse = $kernel->handle($authCheckRequest);
    
    echo "Auth check status: " . $authCheckResponse->getStatusCode() . "\n";
    if ($authCheckResponse->getStatusCode() === 302) {
        $redirectLocation = $authCheckResponse->headers->get('Location');
        if (strpos($redirectLocation, 'login') !== false) {
            echo "User NOT authenticated (redirected to login)\n";
        } else {
            echo "User authenticated (redirected to: " . $redirectLocation . ")\n";
        }
    } elseif ($authCheckResponse->getStatusCode() === 200) {
        echo "User authenticated (dashboard accessible)\n";
    }
} catch (Exception $e) {
    echo "Error checking auth: " . $e->getMessage() . "\n";
}

$kernel->terminate($getRequest, $getResponse);
$kernel->terminate($postRequest, $postResponse);
