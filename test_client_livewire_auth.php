<?php

echo "=== CLIENT LIVEWIRE AUTHENTICATION TEST ===\n";

$baseUrl = 'http://invoices.sample.com';
$clientLoginUrl = $baseUrl . '/client/login';
$cookieJar = tempnam(sys_get_temp_dir(), 'client_cookies');

function testClientLivewireAuth() {
    global $clientLoginUrl, $cookieJar;
    
    echo "Step 1: Getting client login page and extracting Livewire data...\n";
    
    // Get client login page
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $clientLoginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "✗ Client login page failed to load: HTTP $httpCode\n";
        return false;
    }
    
    echo "✓ Client login page loaded successfully\n";
    
    // Extract CSRF token
    if (preg_match('/name="csrf-token"[^>]*content="([^"]*)"/', $response, $matches)) {
        $csrfToken = $matches[1];
        echo "✓ CSRF token extracted: " . substr($csrfToken, 0, 20) . "...\n";
    } else {
        echo "✗ CSRF token not found\n";
        return false;
    }
    
    // Extract Livewire component data
    if (preg_match('/wire:id="([^"]*)"/', $response, $matches)) {
        $livewireId = $matches[1];
        echo "✓ Livewire component ID found: $livewireId\n";
    } else {
        echo "✗ Livewire component ID not found\n";
        return false;
    }
    
    // Extract initial Livewire data
    if (preg_match('/wire:initial-data="([^"]*)"/', $response, $matches)) {
        $initialData = html_entity_decode($matches[1]);
        echo "✓ Livewire initial data found\n";
    } else {
        echo "✗ Livewire initial data not found\n";
        return false;
    }
    
    echo "\nStep 2: Testing Livewire authentication call...\n";
    
    // Prepare Livewire authentication payload
    $livewirePayload = [
        'fingerprint' => [
            'id' => $livewireId,
            'name' => 'filament.pages.auth.login',
            'locale' => 'en',
            'path' => 'client/login',
            'method' => 'GET'
        ],
        'serverMemo' => json_decode($initialData, true),
        'updates' => [
            [
                'type' => 'callMethod',
                'payload' => [
                    'method' => 'authenticate',
                    'params' => []
                ]
            ]
        ]
    ];
    
    // Set form data in the component state
    $livewirePayload['serverMemo']['data']['data']['email'] = '<EMAIL>';
    $livewirePayload['serverMemo']['data']['data']['password'] = 'password';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://invoices.sample.com/livewire/update',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($livewirePayload),
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
            'X-Livewire: true',
        ],
        CURLOPT_TIMEOUT => 15,
    ]);
    
    $livewireResponse = curl_exec($ch);
    $livewireHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Livewire authentication HTTP code: $livewireHttpCode\n";
    
    if ($livewireHttpCode === 200) {
        echo "✓ Livewire authentication call successful\n";
        
        $responseData = json_decode($livewireResponse, true);
        
        // Check for redirect in Livewire response
        if (isset($responseData['effects']['redirect'])) {
            $redirectUrl = $responseData['effects']['redirect'];
            echo "✓ Authentication successful - redirect to: $redirectUrl\n";
            
            // Test accessing the redirect URL
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $redirectUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_COOKIEJAR => $cookieJar,
                CURLOPT_COOKIEFILE => $cookieJar,
                CURLOPT_TIMEOUT => 10,
            ]);
            
            $dashboardResponse = curl_exec($ch);
            $dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($dashboardHttpCode === 200) {
                echo "✓ Client dashboard accessible after authentication\n";
                return true;
            } else {
                echo "✗ Client dashboard not accessible (HTTP $dashboardHttpCode)\n";
                return false;
            }
        } else {
            echo "✗ No redirect found in Livewire response\n";
            echo "Response: " . substr($livewireResponse, 0, 500) . "\n";
            return false;
        }
    } else {
        echo "✗ Livewire authentication failed (HTTP $livewireHttpCode)\n";
        echo "Response: " . substr($livewireResponse, 0, 500) . "\n";
        return false;
    }
}

// Run the test
$success = testClientLivewireAuth();

// Clean up
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

if ($success) {
    echo "\n🎉 SUCCESS: Client Livewire authentication is working!\n";
    echo "The client login page should work properly in a browser.\n";
    echo "Test credentials: <EMAIL> / password\n";
} else {
    echo "\n❌ FAILED: Client Livewire authentication has issues.\n";
    echo "Please test manually in the browser at: http://invoices.sample.com/client/login\n";
}

echo "\n=== CLIENT LIVEWIRE AUTHENTICATION TEST COMPLETE ===\n";
