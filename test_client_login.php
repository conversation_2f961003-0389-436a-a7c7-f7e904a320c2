<?php

echo "=== CLIENT LOGIN TEST ===\n";

$baseUrl = 'http://invoices.sample.com';
$clientLoginUrl = $baseUrl . '/client/login';
$cookieJar = tempnam(sys_get_temp_dir(), 'client_cookies');

function testClientLoginFlow() {
    global $clientLoginUrl, $cookieJar;
    
    echo "Step 1: Testing client login page accessibility...\n";
    
    // Get client login page
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $clientLoginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "✗ Client login page failed to load: HTTP $httpCode\n";
        return false;
    }
    
    echo "✓ Client login page loaded successfully\n";
    
    // Check for Livewire script
    if (preg_match('/src="([^"]*livewire[^"]*)"/', $response, $matches)) {
        echo "✓ Livewire script found: {$matches[1]}\n";
        
        // Test if Livewire script is accessible
        $livewireUrl = $matches[1];
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $livewireUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_NOBODY => true,
            CURLOPT_TIMEOUT => 5,
        ]);
        
        curl_exec($ch);
        $livewireHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($livewireHttpCode === 200) {
            echo "✓ Livewire JavaScript is accessible\n";
        } else {
            echo "✗ Livewire JavaScript not accessible (HTTP $livewireHttpCode)\n";
            return false;
        }
    } else {
        echo "✗ Livewire script not found in client login page\n";
        return false;
    }
    
    // Check for login form
    if (strpos($response, 'wire:submit="authenticate"') !== false) {
        echo "✓ Livewire login form found\n";
    } else {
        echo "✗ Livewire login form not found\n";
        return false;
    }
    
    // Extract CSRF token
    if (preg_match('/name="csrf-token"[^>]*content="([^"]*)"/', $response, $matches)) {
        $csrfToken = $matches[1];
        echo "✓ CSRF token extracted\n";
    } else {
        echo "✗ CSRF token not found\n";
        return false;
    }
    
    echo "\nStep 2: Testing client Livewire endpoint...\n";
    
    // Test if client Livewire update endpoint is accessible
    $livewireUrl = 'http://invoices.sample.com/client/livewire/update';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $livewireUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{}',
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
            'X-Livewire: true',
        ],
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $livewireResponse = curl_exec($ch);
    $livewireHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Client Livewire endpoint HTTP code: $livewireHttpCode\n";
    
    if ($livewireHttpCode === 200 || $livewireHttpCode === 422) {
        echo "✓ Client Livewire endpoint is accessible\n";
    } else {
        echo "✗ Client Livewire endpoint not accessible\n";
        echo "Response: " . substr($livewireResponse, 0, 200) . "\n";
        return false;
    }
    
    echo "\nStep 3: Testing client authentication with test user...\n";
    
    // Get the test client user we created earlier
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    $testUser = DB::table('users')
        ->where('email', '<EMAIL>')
        ->first();
    
    if (!$testUser) {
        echo "✗ Test client user not found\n";
        return false;
    }
    
    echo "✓ Test client user found: {$testUser->email}\n";
    
    // Check if user has client role
    $hasClientRole = DB::table('model_has_roles')
        ->where('model_type', 'App\\Models\\User')
        ->where('model_id', $testUser->id)
        ->where('role_id', 2) // CLIENT role
        ->exists();
    
    if ($hasClientRole) {
        echo "✓ Test user has client role\n";
    } else {
        echo "✗ Test user does not have client role\n";
        return false;
    }
    
    echo "\n✅ ALL CLIENT LOGIN TESTS PASSED!\n";
    echo "The client login page should now work properly in a browser.\n";
    echo "Livewire JavaScript is loading and the authentication endpoint is accessible.\n";
    
    return true;
}

// Run the test
$success = testClientLoginFlow();

// Clean up
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

if ($success) {
    echo "\n🎉 SUCCESS: Client login functionality is working!\n";
    echo "You can now test client login at: http://invoices.sample.com/client/login\n";
    echo "Test credentials: <EMAIL> / password\n";
} else {
    echo "\n❌ FAILED: There are still issues with the client login functionality.\n";
}

echo "\n=== CLIENT LOGIN TEST COMPLETE ===\n";
