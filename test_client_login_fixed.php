<?php

echo "=== CLIENT LOGIN FIXED TEST ===\n";

$baseUrl = 'http://invoices.sample.com';
$clientLoginUrl = $baseUrl . '/client/login';
$cookieJar = tempnam(sys_get_temp_dir(), 'client_cookies');

function testClientLoginFlowFixed() {
    global $clientLoginUrl, $cookieJar;
    
    echo "Step 1: Testing client login page accessibility...\n";
    
    // Get client login page
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $clientLoginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "✗ Client login page failed to load: HTTP $httpCode\n";
        return false;
    }
    
    echo "✓ Client login page loaded successfully\n";
    
    // Extract CSRF token
    if (preg_match('/name="csrf-token"[^>]*content="([^"]*)"/', $response, $matches)) {
        $csrfToken = $matches[1];
        echo "✓ CSRF token extracted\n";
    } else {
        echo "✗ CSRF token not found\n";
        return false;
    }
    
    echo "\nStep 2: Testing correct Livewire endpoint (global route)...\n";
    
    // Test the global Livewire update endpoint (not panel-specific)
    $livewireUrl = 'http://invoices.sample.com/livewire/update';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $livewireUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{}',
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
            'X-Livewire: true',
        ],
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $livewireResponse = curl_exec($ch);
    $livewireHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Global Livewire endpoint HTTP code: $livewireHttpCode\n";
    
    if ($livewireHttpCode === 200 || $livewireHttpCode === 422) {
        echo "✓ Global Livewire endpoint is accessible\n";
    } else {
        echo "✗ Global Livewire endpoint not accessible\n";
        echo "Response: " . substr($livewireResponse, 0, 200) . "\n";
        return false;
    }
    
    echo "\nStep 3: Testing actual client login attempt...\n";
    
    // Get the test client user we created earlier
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    $testUser = DB::table('users')
        ->where('email', '<EMAIL>')
        ->first();
    
    if (!$testUser) {
        echo "✗ Test client user not found\n";
        return false;
    }
    
    echo "✓ Test client user found: {$testUser->email}\n";
    
    // Try to simulate a login attempt by posting to the client login page
    $loginData = [
        'email' => '<EMAIL>',
        'password' => 'password',
        '_token' => $csrfToken
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $clientLoginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => http_build_query($loginData),
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects to see what happens
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
        ],
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $loginResponse = curl_exec($ch);
    $loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    curl_close($ch);
    
    echo "Login attempt HTTP code: $loginHttpCode\n";
    
    if ($redirectUrl) {
        echo "Redirect URL: $redirectUrl\n";
    }
    
    // Check if we got redirected to client dashboard (successful login)
    if ($loginHttpCode === 302 && strpos($redirectUrl, '/client') !== false) {
        echo "✓ Login appears successful (redirected to client area)\n";
        
        // Try to access the redirect URL to confirm
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $redirectUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_COOKIEJAR => $cookieJar,
            CURLOPT_COOKIEFILE => $cookieJar,
            CURLOPT_TIMEOUT => 10,
        ]);
        
        $dashboardResponse = curl_exec($ch);
        $dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($dashboardHttpCode === 200) {
            echo "✓ Client dashboard accessible after login\n";
            return true;
        } else {
            echo "✗ Client dashboard not accessible (HTTP $dashboardHttpCode)\n";
            return false;
        }
    } else {
        echo "✗ Login failed or unexpected response\n";
        echo "Response snippet: " . substr($loginResponse, 0, 300) . "\n";
        return false;
    }
}

// Run the test
$success = testClientLoginFlowFixed();

// Clean up
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

if ($success) {
    echo "\n🎉 SUCCESS: Client login functionality is working!\n";
    echo "You can now test client login at: http://invoices.sample.com/client/login\n";
    echo "Test credentials: <EMAIL> / password\n";
} else {
    echo "\n❌ FAILED: There are still issues with the client login functionality.\n";
}

echo "\n=== CLIENT LOGIN FIXED TEST COMPLETE ===\n";
