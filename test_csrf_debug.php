<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use App\Http\Middleware\VerifyCsrfToken as AppVerifyCsrfToken;

try {
    echo "=== CSRF TOKEN DEBUG TEST ===\n";
    
    // Bootstrap the application
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->bootstrapWith([
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ]);
    
    echo "✓ Application bootstrapped\n";
    
    // Start session
    Session::start();
    echo "✓ Session started\n";
    echo "Session ID: " . Session::getId() . "\n";
    
    // Generate CSRF token
    $token = csrf_token();
    echo "Generated CSRF token: " . substr($token, 0, 20) . "...\n";
    
    // Check session token
    $sessionToken = Session::token();
    echo "Session token: " . substr($sessionToken, 0, 20) . "...\n";
    echo "Tokens match: " . ($token === $sessionToken ? 'YES' : 'NO') . "\n";
    
    // Create a request with the token
    $request = Request::create('http://invoices.sample.com/admin/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password',
        '_token' => $token
    ]);

    // Set the session on the request
    $request->setLaravelSession(Session::getFacadeRoot());

    // Set the request in the application
    $app->instance('request', $request);
    
    echo "\n--- CSRF Middleware Test ---\n";
    
    // Test the CSRF middleware
    $encrypter = $app->make('encrypter');
    $csrfMiddleware = new AppVerifyCsrfToken($app, $encrypter);
    
    try {
        // Create a simple next closure
        $next = function ($request) {
            return response('OK');
        };
        
        // Test the middleware
        $response = $csrfMiddleware->handle($request, $next);
        echo "✓ CSRF middleware passed\n";
        echo "Response: " . $response->getContent() . "\n";
        
    } catch (\Illuminate\Session\TokenMismatchException $e) {
        echo "✗ CSRF middleware failed: Token mismatch\n";
        echo "Error: " . $e->getMessage() . "\n";
    } catch (Exception $e) {
        echo "✗ CSRF middleware error: " . $e->getMessage() . "\n";
    }
    
    // Check excluded routes
    echo "\n--- Excluded Routes Check ---\n";
    $reflection = new ReflectionClass($csrfMiddleware);
    $exceptProperty = $reflection->getProperty('except');
    $exceptProperty->setAccessible(true);
    $excludedRoutes = $exceptProperty->getValue($csrfMiddleware);
    
    echo "Excluded routes:\n";
    foreach ($excludedRoutes as $route) {
        echo "  - $route\n";
    }
    
    // Test if current route is excluded
    $isExcluded = false;
    foreach ($excludedRoutes as $except) {
        if ($request->is($except)) {
            $isExcluded = true;
            break;
        }
    }
    echo "Current route excluded: " . ($isExcluded ? 'YES' : 'NO') . "\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
