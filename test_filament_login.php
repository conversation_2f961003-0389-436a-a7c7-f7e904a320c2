<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\User;
use App\Http\Responses\LoginResponse;

try {
    echo "=== FILAMENT LOGIN SIMULATION ===\n";
    
    // Bootstrap the application
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->bootstrapWith([
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ]);
    
    echo "✓ Application bootstrapped\n";
    
    // Create a mock request
    $request = Request::create('http://invoices.sample.com/admin/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password',
        '_token' => 'test-token'
    ]);
    
    // Set the request in the application
    $app->instance('request', $request);
    
    // Start session
    Session::start();
    echo "✓ Session started\n";
    
    // Try authentication
    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password'
    ];
    
    $authResult = Auth::attempt($credentials);
    echo "Auth::attempt result: " . ($authResult ? 'SUCCESS' : 'FAILED') . "\n";
    
    if ($authResult) {
        echo "✓ Authentication successful\n";
        $user = Auth::user();
        echo "Authenticated user: " . $user->email . "\n";
        echo "User ID: " . Auth::id() . "\n";
        
        // Test the LoginResponse
        echo "\n--- Testing LoginResponse ---\n";
        $loginResponse = new LoginResponse();
        $response = $loginResponse->toResponse($request);
        
        echo "Response type: " . get_class($response) . "\n";
        if (method_exists($response, 'getTargetUrl')) {
            echo "Redirect URL: " . $response->getTargetUrl() . "\n";
        }
        
        // Check user role
        $role = $user->roles()->first();
        if ($role) {
            echo "User role name: " . $role->name . "\n";
            echo "Role matches ROLE_ADMIN: " . ($role->name === \App\Models\Role::ROLE_ADMIN ? 'YES' : 'NO') . "\n";
        }
        
    } else {
        echo "✗ Authentication failed\n";
        
        // Check what went wrong
        $user = User::where('email', '<EMAIL>')->first();
        if (!$user) {
            echo "User not found\n";
        } else {
            echo "User found, checking password...\n";
            $passwordCheck = \Illuminate\Support\Facades\Hash::check('password', $user->password);
            echo "Password check: " . ($passwordCheck ? 'PASS' : 'FAIL') . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
