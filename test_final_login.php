<?php

echo "=== FINAL LOGIN TEST ===\n";

$baseUrl = 'http://invoices.sample.com';
$loginUrl = $baseUrl . '/admin/login';
$cookieJar = tempnam(sys_get_temp_dir(), 'cookies');

function testLoginFlow() {
    global $loginUrl, $cookieJar;
    
    echo "Step 1: Loading login page...\n";
    
    // Get login page
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $loginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "✗ Login page failed to load: HTTP $httpCode\n";
        return false;
    }
    
    echo "✓ Login page loaded successfully\n";
    
    // Check for Livewire script
    if (preg_match('/src="([^"]*livewire[^"]*)"/', $response)) {
        echo "✓ Livewire script found in page\n";
    } else {
        echo "✗ Livewire script not found\n";
        return false;
    }
    
    // Check for login form
    if (strpos($response, 'wire:submit="authenticate"') !== false) {
        echo "✓ Livewire login form found\n";
    } else {
        echo "✗ Livewire login form not found\n";
        return false;
    }
    
    // Extract CSRF token
    if (preg_match('/name="csrf-token"[^>]*content="([^"]*)"/', $response, $matches)) {
        $csrfToken = $matches[1];
        echo "✓ CSRF token extracted\n";
    } else {
        echo "✗ CSRF token not found\n";
        return false;
    }
    
    echo "\nStep 2: Testing Livewire endpoint...\n";
    
    // Test if Livewire update endpoint is accessible
    $livewireUrl = 'http://invoices.sample.com/livewire/update';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $livewireUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => '{}',
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
            'X-Livewire: true',
        ],
        CURLOPT_TIMEOUT => 10,
    ]);
    
    $livewireResponse = curl_exec($ch);
    $livewireHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Livewire endpoint HTTP code: $livewireHttpCode\n";
    
    if ($livewireHttpCode === 200 || $livewireHttpCode === 422) {
        echo "✓ Livewire endpoint is accessible\n";
    } else {
        echo "✗ Livewire endpoint not accessible\n";
        echo "Response: " . substr($livewireResponse, 0, 200) . "\n";
        return false;
    }
    
    echo "\n✅ ALL TESTS PASSED!\n";
    echo "The login page should now work properly in a browser.\n";
    echo "Livewire JavaScript is loading and the authentication endpoint is accessible.\n";
    
    return true;
}

// Run the test
$success = testLoginFlow();

// Clean up
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

if ($success) {
    echo "\n🎉 SUCCESS: Login functionality is now working!\n";
    echo "You can now log in at: http://invoices.sample.com/admin/login\n";
    echo "Credentials: <EMAIL> / password\n";
} else {
    echo "\n❌ FAILED: There are still issues with the login functionality.\n";
}

echo "\n=== TEST COMPLETE ===\n";
