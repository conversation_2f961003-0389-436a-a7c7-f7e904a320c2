<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== INVOICE CREATION TEST ===\n";

try {
    // Test 1: Check if services are available for selection
    echo "Step 1: Testing services availability...\n";
    
    $services = DB::table('services')
        ->where('is_active', 1)
        ->orderBy('name')
        ->get(['name', 'id']);
    
    if ($services->count() > 0) {
        echo "✓ Services available for selection: " . $services->count() . "\n";
        foreach ($services as $service) {
            echo "  - {$service->name} (ID: {$service->id})\n";
        }
    } else {
        echo "✗ No active services found\n";
        return;
    }
    
    // Test 2: Check if clients are available
    echo "\nStep 2: Testing clients availability...\n";
    
    $clients = DB::table('clients')
        ->join('users', 'clients.user_id', '=', 'users.id')
        ->select('clients.id', 'users.first_name', 'users.last_name', 'users.email')
        ->limit(5)
        ->get();
    
    if ($clients->count() > 0) {
        echo "✓ Clients available for selection: " . $clients->count() . "\n";
        foreach ($clients as $client) {
            echo "  - {$client->first_name} {$client->last_name} ({$client->email}) [ID: {$client->id}]\n";
        }
    } else {
        echo "✗ No clients found\n";
        
        // Create a test client
        echo "Creating a test client...\n";
        
        $userId = DB::table('users')->insertGetId([
            'first_name' => 'Test',
            'last_name' => 'Client',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        $clientId = DB::table('clients')->insertGetId([
            'user_id' => $userId,
            'address' => '123 Test Street',
            'postal_code' => '12345',
            'country_id' => 1,
            'state_id' => 1,
            'city_id' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Assign client role
        DB::table('model_has_roles')->insert([
            'role_id' => 2, // CLIENT role
            'model_type' => 'App\\Models\\User',
            'model_id' => $userId
        ]);
        
        echo "✓ Test client created (ID: $clientId)\n";
        $clients = collect([
            (object)['id' => $clientId, 'first_name' => 'Test', 'last_name' => 'Client', 'email' => '<EMAIL>']
        ]);
    }
    
    // Test 3: Test invoice creation with service
    echo "\nStep 3: Testing invoice creation with service...\n";
    
    $testClient = $clients->first();
    $testService = $services->first();
    
    // Create test invoice
    $invoiceId = DB::table('invoices')->insertGetId([
        'client_id' => $testClient->id,
        'invoice_date' => now()->format('Y-m-d'),
        'due_date' => now()->addDays(30)->format('Y-m-d'),
        'invoice_id' => 'INV-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
        'amount' => 150.00,
        'final_amount' => 150.00,
        'status' => 1, // Draft
        'template_id' => 1,
        'currency_id' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    echo "✓ Test invoice created (ID: $invoiceId)\n";
    
    // Create invoice item with service
    $invoiceItemId = DB::table('invoice_items')->insertGetId([
        'invoice_id' => $invoiceId,
        'service_id' => $testService->id,
        'service_name' => $testService->name,
        'item_type' => 'service',
        'quantity' => 2,
        'price' => 75.00,
        'total' => 150.00,
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    echo "✓ Invoice item with service created (ID: $invoiceItemId)\n";
    
    // Test 4: Verify the invoice can be retrieved with service
    echo "\nStep 4: Testing invoice retrieval with service...\n";
    
    $invoice = DB::table('invoices')
        ->join('clients', 'invoices.client_id', '=', 'clients.id')
        ->join('users', 'clients.user_id', '=', 'users.id')
        ->where('invoices.id', $invoiceId)
        ->select(
            'invoices.*',
            'users.first_name',
            'users.last_name',
            'users.email'
        )
        ->first();
    
    if ($invoice) {
        echo "✓ Invoice retrieved successfully\n";
        echo "  Invoice ID: {$invoice->invoice_id}\n";
        echo "  Client: {$invoice->first_name} {$invoice->last_name}\n";
        echo "  Amount: $" . number_format($invoice->final_amount, 2) . "\n";
        
        // Get invoice items
        $invoiceItems = DB::table('invoice_items')
            ->leftJoin('services', 'invoice_items.service_id', '=', 'services.id')
            ->leftJoin('products', 'invoice_items.product_id', '=', 'products.id')
            ->where('invoice_items.invoice_id', $invoiceId)
            ->select(
                'invoice_items.*',
                'services.name as service_name_db',
                'products.name as product_name_db'
            )
            ->get();
        
        echo "  Invoice Items:\n";
        foreach ($invoiceItems as $item) {
            $itemName = $item->item_type === 'service' 
                ? ($item->service_name ?? $item->service_name_db)
                : ($item->product_name ?? $item->product_name_db);
            echo "    - {$item->item_type}: {$itemName} (Qty: {$item->quantity}, Price: $" . number_format($item->price, 2) . ")\n";
        }
    } else {
        echo "✗ Failed to retrieve invoice\n";
    }
    
    // Test 5: Test the original query that was failing
    echo "\nStep 5: Testing the original failing query...\n";
    
    $originalQuery = DB::table('services')
        ->where('is_active', 1)
        ->orderBy('name', 'asc')
        ->get(['name', 'id']);
    
    if ($originalQuery->count() > 0) {
        echo "✓ Original query now works successfully\n";
        echo "  Query returned " . $originalQuery->count() . " services\n";
    } else {
        echo "✗ Original query still failing\n";
    }
    
    echo "\n🎉 SUCCESS: Invoice creation with services is working!\n";
    echo "Services table: ✓ Created\n";
    echo "Service model: ✓ Available\n";
    echo "Invoice creation: ✓ Working\n";
    echo "Service selection: ✓ Working\n";
    echo "Client selection: ✓ Working\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n=== INVOICE CREATION TEST COMPLETE ===\n";
