<?php

echo "=== LIVEWIRE ASSETS TEST ===\n";

$loginUrl = 'http://invoices.sample.com/admin/login';
$livewireJsUrl = 'http://invoices.sample.com/livewire/livewire.js';

// Test 1: Check if login page loads and has correct Livewire script
echo "Step 1: Checking login page Livewire script tag...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $loginUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 10,
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "✗ Login page failed to load: HTTP $httpCode\n";
    exit(1);
}

echo "✓ Login page loaded successfully\n";

// Check for Livewire script tag
if (preg_match('/<script[^>]*src="([^"]*livewire[^"]*)"/', $response, $matches)) {
    $scriptSrc = $matches[1];
    echo "✓ Livewire script found: $scriptSrc\n";
    
    // Test 2: Check if Livewire JavaScript file is accessible
    echo "\nStep 2: Testing Livewire JavaScript accessibility...\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $scriptSrc,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HEADER => true,
    ]);
    
    $jsResponse = curl_exec($ch);
    $jsHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    echo "Livewire JS HTTP code: $jsHttpCode\n";
    echo "Content-Type: $contentType\n";
    
    if ($jsHttpCode === 200) {
        echo "✓ Livewire JavaScript is accessible\n";
        
        // Check if it contains Livewire code
        if (strpos($jsResponse, 'Livewire') !== false || strpos($jsResponse, 'livewire') !== false) {
            echo "✓ Livewire JavaScript contains expected content\n";
        } else {
            echo "✗ Livewire JavaScript doesn't contain expected content\n";
            echo "First 200 chars: " . substr($jsResponse, 0, 200) . "\n";
        }
    } else {
        echo "✗ Livewire JavaScript is not accessible\n";
        echo "Response: " . substr($jsResponse, 0, 500) . "\n";
    }
} else {
    echo "✗ Livewire script tag not found in login page\n";
    echo "Looking for script tags...\n";
    
    if (preg_match_all('/<script[^>]*src="([^"]*)"/', $response, $allMatches)) {
        echo "Found script sources:\n";
        foreach ($allMatches[1] as $src) {
            echo "  - $src\n";
        }
    } else {
        echo "No script tags with src found\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
