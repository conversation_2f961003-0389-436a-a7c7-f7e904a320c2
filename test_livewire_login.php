<?php

// Test Livewire-based login
function testLivewireLogin() {
    $baseUrl = 'http://invoices.sample.com';
    $loginUrl = $baseUrl . '/admin/login';
    $livewireUrl = $baseUrl . '/livewire/update';
    
    echo "=== LIVEWIRE LOGIN TEST ===\n";
    
    // Create a temporary cookie jar
    $cookieJar = tempnam(sys_get_temp_dir(), 'cookies');
    
    // Step 1: Get the login page to extract Livewire component data
    echo "Step 1: Getting login page for Livewire data...\n";
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $loginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER => true,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_COOKIEJAR => $cookieJar,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    curl_close($ch);
    
    echo "Login page HTTP code: $httpCode\n";
    
    if ($httpCode !== 200) {
        echo "✗ Failed to load login page\n";
        return false;
    }
    
    // Extract CSRF token
    preg_match('/name="csrf-token" content="([^"]+)"/', $body, $csrfMatches);
    if (empty($csrfMatches[1])) {
        echo "✗ CSRF token not found\n";
        return false;
    }
    $csrfToken = $csrfMatches[1];
    echo "✓ CSRF token extracted: " . substr($csrfToken, 0, 10) . "...\n";
    
    // Extract Livewire component data for the login form
    preg_match_all('/wire:snapshot="([^"]+)"/', $body, $snapshotMatches);
    if (empty($snapshotMatches[1])) {
        echo "✗ Livewire snapshot not found\n";
        return false;
    }

    // Find the login form component (filament.pages.auth.login)
    $loginSnapshot = null;
    foreach ($snapshotMatches[1] as $snapshotData) {
        $decodedSnapshot = html_entity_decode($snapshotData);
        $snapshot = json_decode($decodedSnapshot, true);
        if ($snapshot && isset($snapshot['memo']['name']) && $snapshot['memo']['name'] === 'filament.pages.auth.login') {
            $loginSnapshot = $decodedSnapshot;
            break;
        }
    }

    if (!$loginSnapshot) {
        echo "✗ Login form component not found\n";
        return false;
    }
    
    $snapshot = json_decode($loginSnapshot, true);

    if (!$snapshot) {
        echo "✗ Failed to parse Livewire snapshot\n";
        return false;
    }

    echo "✓ Livewire component found: " . $snapshot['memo']['name'] . "\n";
    echo "✓ Component ID: " . $snapshot['memo']['id'] . "\n";
    
    // Step 2: Submit via Livewire
    echo "\nStep 2: Submitting via Livewire...\n";
    
    // Prepare Livewire payload
    $livewirePayload = [
        'fingerprint' => [
            'id' => $snapshot['memo']['id'],
            'name' => $snapshot['memo']['name'],
            'locale' => $snapshot['memo']['locale'],
            'path' => $snapshot['memo']['path'],
            'method' => $snapshot['memo']['method'],
        ],
        'serverMemo' => [
            'children' => $snapshot['memo']['children'],
            'errors' => $snapshot['memo']['errors'],
            'htmlHash' => $snapshot['checksum'],
            'data' => $snapshot['data'],
            'dataMeta' => [],
            'checksum' => $snapshot['checksum'],
        ],
        'updates' => [
            [
                'type' => 'syncInput',
                'payload' => [
                    'name' => 'data.email',
                    'value' => '<EMAIL>'
                ]
            ],
            [
                'type' => 'syncInput',
                'payload' => [
                    'name' => 'data.password',
                    'value' => 'password'
                ]
            ],
            [
                'type' => 'callMethod',
                'payload' => [
                    'method' => 'authenticate',
                    'params' => []
                ]
            ]
        ]
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $livewireUrl,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($livewirePayload),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_HEADER => true,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'X-Requested-With: XMLHttpRequest',
            'X-CSRF-TOKEN: ' . $csrfToken,
            'X-Livewire: true',
            'Referer: ' . $loginUrl,
        ],
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    curl_close($ch);
    
    echo "Livewire POST HTTP code: $httpCode\n";
    
    if ($httpCode === 200) {
        echo "✓ Livewire request successful\n";
        
        // Parse Livewire response
        $livewireResponse = json_decode($body, true);
        if ($livewireResponse) {
            echo "Livewire response received\n";
            
            // Check for redirects in the response
            if (isset($livewireResponse['effects']['redirect'])) {
                echo "✓ Redirect found: " . $livewireResponse['effects']['redirect'] . "\n";
                return true;
            } elseif (isset($livewireResponse['effects']['html'])) {
                echo "HTML update received (likely form validation errors)\n";
                return false;
            } else {
                echo "Unknown Livewire response structure\n";
                echo "Full response: " . $body . "\n";

                // Check if there are any effects at all
                if (isset($livewireResponse['effects'])) {
                    echo "Effects found: " . json_encode($livewireResponse['effects']) . "\n";
                } else {
                    echo "No effects in response\n";
                }
                return false;
            }
        } else {
            echo "✗ Failed to parse Livewire response\n";
            echo "Response: " . substr($body, 0, 200) . "...\n";
            return false;
        }
    } else {
        echo "✗ Livewire request failed\n";
        echo "Response: " . substr($body, 0, 200) . "...\n";
        return false;
    }
    
    // Clean up
    unlink($cookieJar);
}

// Run the test
if (testLivewireLogin()) {
    echo "\n✓ LIVEWIRE LOGIN TEST PASSED\n";
} else {
    echo "\n✗ LIVEWIRE LOGIN TEST FAILED\n";
}
