<?php

// Test script to debug login issues
require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a test request to the login page
$request = Request::create('http://invoices.sample.com/admin/login', 'GET');
$response = $kernel->handle($request);

echo "=== LOGIN PAGE TEST ===\n";
echo "Status Code: " . $response->getStatusCode() . "\n";
echo "Content-Type: " . $response->headers->get('Content-Type') . "\n";

// Check for CSRF token in response
$content = $response->getContent();
if (preg_match('/name="csrf-token" content="([^"]+)"/', $content, $matches)) {
    echo "CSRF Token Found: " . $matches[1] . "\n";
} else {
    echo "CSRF Token: NOT FOUND\n";
}

// Check for Livewire components
if (strpos($content, 'wire:') !== false) {
    echo "Livewire Components: FOUND\n";
} else {
    echo "Livewire Components: NOT FOUND\n";
}

// Check session configuration
echo "\n=== SESSION CONFIGURATION ===\n";
echo "Session Driver: " . config('session.driver') . "\n";
echo "Session Domain: " . config('session.domain') . "\n";
echo "Session Cookie: " . config('session.cookie') . "\n";
echo "Session Path: " . config('session.path') . "\n";
echo "Session Same Site: " . config('session.same_site') . "\n";

// Check CSRF configuration
echo "\n=== CSRF CONFIGURATION ===\n";
echo "APP_KEY set: " . (config('app.key') ? 'YES' : 'NO') . "\n";
echo "APP_URL: " . config('app.url') . "\n";

// Test CSRF token generation
try {
    $token = csrf_token();
    echo "CSRF Token Generation: SUCCESS (" . substr($token, 0, 10) . "...)\n";
} catch (Exception $e) {
    echo "CSRF Token Generation: FAILED - " . $e->getMessage() . "\n";
}

echo "\n=== MIDDLEWARE STACK ===\n";
$middlewareGroups = config('app.middleware_groups', []);
if (isset($middlewareGroups['web'])) {
    echo "Web Middleware:\n";
    foreach ($middlewareGroups['web'] as $middleware) {
        echo "  - " . $middleware . "\n";
    }
}

$kernel->terminate($request, $response);
