<?php

// Test script to debug login POST request
require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== TESTING LOGIN PROCESS ===\n";

// Step 1: Get the login page and extract CSRF token
echo "Step 1: Getting login page...\n";
$getRequest = Request::create('http://invoices.sample.com/admin/login', 'GET');
$getResponse = $kernel->handle($getRequest);

if ($getResponse->getStatusCode() !== 200) {
    echo "ERROR: Login page returned status " . $getResponse->getStatusCode() . "\n";
    exit(1);
}

// Extract CSRF token
$content = $getResponse->getContent();
if (preg_match('/name="csrf-token" content="([^"]+)"/', $content, $matches)) {
    $csrfToken = $matches[1];
    echo "CSRF Token extracted: " . substr($csrfToken, 0, 20) . "...\n";
} else {
    echo "ERROR: Could not extract CSRF token\n";
    exit(1);
}

// Extract session cookies
$cookies = [];
foreach ($getResponse->headers->getCookies() as $cookie) {
    $cookies[$cookie->getName()] = $cookie->getValue();
    echo "Cookie set: " . $cookie->getName() . " = " . substr($cookie->getValue(), 0, 20) . "...\n";
}

echo "\nStep 2: Testing Livewire login...\n";

// Step 2: Simulate Livewire login request
$livewireData = [
    'fingerprint' => [
        'id' => 'test-component-id',
        'name' => 'filament.pages.auth.login',
        'locale' => 'en',
        'path' => 'admin/login',
        'method' => 'GET'
    ],
    'serverMemo' => [
        'data' => [
            'data' => [
                'email' => '<EMAIL>',
                'password' => 'password',
                'remember' => false
            ]
        ]
    ],
    'updates' => [
        [
            'type' => 'callMethod',
            'payload' => [
                'method' => 'authenticate',
                'params' => []
            ]
        ]
    ]
];

// Create POST request with proper headers and content
$postRequest = Request::create(
    'http://invoices.sample.com/livewire/update',
    'POST',
    [],
    $cookies,
    [],
    [],
    json_encode($livewireData)
);
$postRequest->headers->set('Content-Type', 'application/json');
$postRequest->headers->set('X-CSRF-TOKEN', $csrfToken);
$postRequest->headers->set('X-Livewire', 'true');
$postRequest->headers->set('Referer', 'http://invoices.sample.com/admin/login');

echo "Making Livewire request...\n";
$postResponse = $kernel->handle($postRequest);

echo "Response Status: " . $postResponse->getStatusCode() . "\n";
echo "Response Headers:\n";
foreach ($postResponse->headers->all() as $name => $values) {
    echo "  $name: " . implode(', ', $values) . "\n";
}

if ($postResponse->getStatusCode() === 419) {
    echo "\nERROR: CSRF Token Mismatch (419)\n";
    echo "This indicates the CSRF token validation is failing.\n";
    
    // Check session configuration
    echo "\nSession Debug Info:\n";
    echo "Session Domain: " . config('session.domain') . "\n";
    echo "Request Host: " . $postRequest->getHost() . "\n";
    echo "Session Same Site: " . config('session.same_site') . "\n";
    
} elseif ($postResponse->getStatusCode() === 200) {
    echo "\nSUCCESS: Request processed successfully\n";
    $responseContent = $postResponse->getContent();
    if (strpos($responseContent, 'redirect') !== false) {
        echo "Response contains redirect - login likely successful\n";
    }
} else {
    echo "\nUnexpected response code: " . $postResponse->getStatusCode() . "\n";
    echo "Response content: " . substr($postResponse->getContent(), 0, 500) . "\n";
}

$kernel->terminate($getRequest, $getResponse);
$kernel->terminate($postRequest, $postResponse);
