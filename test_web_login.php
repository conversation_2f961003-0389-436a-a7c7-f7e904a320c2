<?php

// Test the actual web login flow with proper session handling
function testWebLogin() {
    $baseUrl = 'http://invoices.sample.com';
    $loginUrl = $baseUrl . '/admin/login';
    $dashboardUrl = $baseUrl . '/admin';
    
    echo "=== WEB LOGIN FLOW TEST ===\n";
    
    // Initialize cURL with cookie jar for session persistence
    $cookieJar = tempnam(sys_get_temp_dir(), 'cookies');
    
    // Step 1: Get login page and extract CSRF token
    echo "Step 1: Getting login page...\n";
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $loginUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_COOKIEJAR => $cookieJar,
        CURLOPT_COOKIEFILE => $cookieJar,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        CURLOPT_HEADER => true,
        CURLOPT_VERBOSE => false,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "Login page HTTP code: $httpCode\n";
    
    if ($httpCode !== 200) {
        echo "✗ Failed to load login page\n";
        curl_close($ch);
        unlink($cookieJar);
        return false;
    }
    
    // Extract CSRF token - try multiple patterns
    preg_match('/name="_token" value="([^"]+)"/', $body, $tokenMatches);
    if (empty($tokenMatches[1])) {
        // Try meta tag
        preg_match('/name="csrf-token" content="([^"]+)"/', $body, $tokenMatches);
    }
    if (empty($tokenMatches[1])) {
        // Try Livewire token
        preg_match('/window\.Laravel\.csrfToken = "([^"]+)"/', $body, $tokenMatches);
    }
    if (empty($tokenMatches[1])) {
        // Try data attribute
        preg_match('/data-csrf="([^"]+)"/', $body, $tokenMatches);
    }
    
    if (empty($tokenMatches[1])) {
        echo "✗ Could not extract CSRF token\n";
        curl_close($ch);
        unlink($cookieJar);
        return false;
    }
    
    $csrfToken = $tokenMatches[1];
    echo "✓ CSRF token extracted: " . substr($csrfToken, 0, 10) . "...\n";

    // Check cookies from the login page
    echo "Cookies from login page:\n";
    preg_match_all('/Set-Cookie: ([^;]+)/', $headers, $cookieMatches);
    foreach ($cookieMatches[1] as $cookie) {
        echo "  $cookie\n";
    }
    
    // Step 2: Submit login form
    echo "\nStep 2: Submitting login form...\n";
    
    $postData = http_build_query([
        'email' => '<EMAIL>',
        'password' => 'password',
        '_token' => $csrfToken,
    ]);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $loginUrl,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects automatically
        CURLOPT_HEADER => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded',
            'Referer: ' . $loginUrl,
        ],
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "Login POST HTTP code: $httpCode\n";
    
    // Check for redirect
    if ($httpCode === 302) {
        preg_match('/Location: (.+)/', $headers, $locationMatches);
        $redirectUrl = isset($locationMatches[1]) ? trim($locationMatches[1]) : 'Unknown';
        echo "Redirect to: $redirectUrl\n";
        
        // Step 3: Follow redirect to check if we're authenticated
        echo "\nStep 3: Following redirect...\n";
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $redirectUrl,
            CURLOPT_POST => false,
            CURLOPT_POSTFIELDS => null,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HEADER => true,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $body = substr($response, $headerSize);
        
        echo "Final HTTP code: $httpCode\n";
        echo "Final URL: $finalUrl\n";
        
        // Check if we're on the dashboard or back at login
        if (strpos($finalUrl, '/admin/login') !== false) {
            echo "✗ Redirected back to login - Authentication failed\n";
            
            // Check for error messages in the response
            if (strpos($body, 'credentials do not match') !== false) {
                echo "Error: Invalid credentials\n";
            } elseif (strpos($body, 'Page Expired') !== false || strpos($body, '419') !== false) {
                echo "Error: CSRF token mismatch\n";
            } else {
                echo "Error: Unknown authentication failure\n";
            }
            
        } elseif (strpos($finalUrl, '/admin') !== false && strpos($finalUrl, '/login') === false) {
            echo "✓ Successfully authenticated and redirected to dashboard\n";
            return true;
        } else {
            echo "? Unexpected redirect destination\n";
        }
        
    } else {
        echo "✗ Expected redirect (302) but got $httpCode\n";
        echo "Response body: " . substr($body, 0, 500) . "\n";
    }
    
    curl_close($ch);
    unlink($cookieJar);
    return false;
}

// Run the test
$success = testWebLogin();
echo "\n" . ($success ? "✓ WEB LOGIN TEST PASSED" : "✗ WEB LOGIN TEST FAILED") . "\n";
