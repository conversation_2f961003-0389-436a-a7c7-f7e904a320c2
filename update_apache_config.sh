#!/bin/bash

# Script to update Apache configuration for virtual hosts
echo "Updating Apache configuration..."

# Backup original files
echo "Creating backups..."
sudo cp /opt/lampp/etc/httpd.conf /opt/lampp/etc/httpd.conf.original.backup
sudo cp /opt/lampp/etc/extra/httpd-vhosts.conf /opt/lampp/etc/extra/httpd-vhosts.conf.original.backup

# Copy modified files
echo "Updating configuration files..."
sudo cp ./httpd.conf.backup /opt/lampp/etc/httpd.conf
sudo cp ./httpd-vhosts.conf.backup /opt/lampp/etc/extra/httpd-vhosts.conf

# Set proper permissions
sudo chown root:root /opt/lampp/etc/httpd.conf
sudo chown root:root /opt/lampp/etc/extra/httpd-vhosts.conf
sudo chmod 644 /opt/lampp/etc/httpd.conf
sudo chmod 644 /opt/lampp/etc/extra/httpd-vhosts.conf

echo "Apache configuration updated successfully!"
echo "Please restart Apache for changes to take effect."
