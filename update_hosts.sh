#!/bin/bash

# <PERSON><PERSON>t to update hosts file for invoices.sample.com
echo "Updating hosts file..."

# Backup original hosts file
sudo cp /etc/hosts /etc/hosts.backup

# Check if invoices.sample.com already exists
if grep -q "invoices.sample.com" /etc/hosts; then
    echo "invoices.sample.com already exists in hosts file"
else
    # Add the new entry
    echo "127.0.0.1       invoices.sample.com" | sudo tee -a /etc/hosts
    echo "127.0.0.1       www.invoices.sample.com" | sudo tee -a /etc/hosts
    echo "Added invoices.sample.com to hosts file"
fi

echo "Current hosts file entries for invoices:"
grep "invoices" /etc/hosts

echo "Hosts file updated successfully!"
